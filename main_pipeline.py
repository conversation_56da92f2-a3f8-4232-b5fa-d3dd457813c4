"""
摩拜单车商业选址推荐系统主执行脚本
"""

import os
import sys
import time
import pandas as pd
from datetime import datetime

# 导入自定义模块
from data_preprocessing import MobikeDataProcessor
from destination_prediction import DestinationPredictor
from business_location_recommender import BusinessLocationRecommender
from visualization import BusinessLocationVisualizer

class MobikeBusinessPipeline:
    """摩拜单车商业选址推荐系统主流程"""
    
    def __init__(self):
        self.start_time = time.time()
        self.log_file = f"pipeline_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
    
    def check_dependencies(self):
        """检查依赖包"""
        self.log("Checking dependencies...")
        
        required_packages = [
            'pandas', 'numpy', 'sklearn', 'matplotlib',
            'seaborn', 'geohash2', 'folium', 'joblib'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            self.log(f"Missing packages: {missing_packages}")
            self.log("Please install missing packages using: pip install " + " ".join(missing_packages))
            return False
        
        self.log("All dependencies satisfied")
        return True
    
    def check_data_files(self):
        """检查数据文件"""
        self.log("Checking data files...")
        
        required_files = ['train.csv', 'test.csv', '北京市POI数据.csv']
        missing_files = []
        
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            self.log(f"Missing data files: {missing_files}")
            return False
        
        self.log("All data files found")
        return True
    
    def step1_data_preprocessing(self):
        """步骤1: 数据预处理"""
        self.log("=== Step 1: Data Preprocessing ===")
        
        try:
            processor = MobikeDataProcessor()
            processor.load_data()
            
            # 处理训练数据
            train_data = processor.process_train_data()
            self.log(f"Processed training data: {train_data.shape}")
            
            # 处理测试数据
            test_data = processor.process_test_data()
            self.log(f"Processed test data: {test_data.shape}")
            
            # 保存处理后的数据
            processor.save_processed_data()
            
            self.log("Data preprocessing completed successfully")
            return True
            
        except Exception as e:
            self.log(f"Error in data preprocessing: {str(e)}")
            return False
    
    def step2_destination_prediction(self):
        """步骤2: 终点预测"""
        self.log("=== Step 2: Destination Prediction ===")
        
        try:
            # 检查预处理数据是否存在
            if not os.path.exists('processed_train.csv') or not os.path.exists('processed_test.csv'):
                self.log("Processed data not found, running preprocessing first...")
                if not self.step1_data_preprocessing():
                    return False
            
            # 加载数据
            train_data = pd.read_csv('processed_train.csv')
            test_data = pd.read_csv('processed_test.csv')
            
            # 采样训练数据（如果数据太大）
            if len(train_data) > 500000:
                self.log("Sampling training data for faster processing...")
                train_data = train_data.sample(n=500000, random_state=42)
            
            # 训练预测模型
            predictor = DestinationPredictor()
            metrics = predictor.train_model(train_data)
            
            self.log(f"Model training completed. Validation metrics: {metrics}")
            
            # 保存模型
            predictor.save_model()
            
            # 预测测试集
            predictions = predictor.predict(test_data)
            predictions.to_csv('destination_predictions.csv', index=False)
            
            self.log(f"Predictions completed: {len(predictions)} records")
            self.log("Destination prediction completed successfully")
            return True
            
        except Exception as e:
            self.log(f"Error in destination prediction: {str(e)}")
            return False
    
    def step3_business_analysis(self):
        """步骤3: 商业选址分析"""
        self.log("=== Step 3: Business Location Analysis ===")
        
        try:
            # 创建推荐系统
            recommender = BusinessLocationRecommender()
            recommender.load_data()
            
            # 执行分析
            grid_df, recommendations = recommender.analyze_business_locations()
            
            self.log(f"Analysis completed: {len(grid_df)} grid cells analyzed")
            self.log(f"Generated {len(recommendations)} recommendations")
            
            # 生成报告
            recommender.generate_report(recommendations)
            
            self.log("Business location analysis completed successfully")
            return True
            
        except Exception as e:
            self.log(f"Error in business analysis: {str(e)}")
            return False
    
    def step4_visualization(self):
        """步骤4: 可视化"""
        self.log("=== Step 4: Visualization ===")
        
        try:
            # 检查分析结果是否存在
            required_files = ['business_location_analysis.csv', 'top_business_locations.csv']
            for file in required_files:
                if not os.path.exists(file):
                    self.log(f"Analysis result file {file} not found, running analysis first...")
                    if not self.step3_business_analysis():
                        return False
                    break
            
            # 加载数据
            grid_df = pd.read_csv('business_location_analysis.csv')
            recommendations = pd.read_csv('top_business_locations.csv')
            poi_data = pd.read_csv('北京市POI数据.csv')
            
            # 创建可视化
            visualizer = BusinessLocationVisualizer()
            
            # 生成各种图表
            self.log("Generating business score heatmap...")
            visualizer.plot_business_score_heatmap(grid_df)
            
            self.log("Generating POI distribution plot...")
            visualizer.plot_poi_distribution(poi_data)
            
            self.log("Generating bike flow heatmap...")
            visualizer.plot_bike_flow_heatmap(grid_df)
            
            self.log("Generating recommendations map...")
            visualizer.plot_recommendations_map(recommendations)
            
            self.log("Generating score distribution plots...")
            visualizer.plot_score_distribution(grid_df)
            
            self.log("Generating summary report...")
            visualizer.generate_summary_report(recommendations, grid_df)
            
            self.log("Creating interactive map...")
            interactive_map = visualizer.create_interactive_map(recommendations, grid_df)
            
            self.log("Visualization completed successfully")
            return True
            
        except Exception as e:
            self.log(f"Error in visualization: {str(e)}")
            return False
    
    def run_full_pipeline(self):
        """运行完整流程"""
        self.log("=== Starting Mobike Business Location Recommendation Pipeline ===")
        
        # 检查环境
        if not self.check_dependencies():
            self.log("Pipeline aborted due to missing dependencies")
            return False
        
        if not self.check_data_files():
            self.log("Pipeline aborted due to missing data files")
            return False
        
        # 执行各步骤
        steps = [
            ("Data Preprocessing", self.step1_data_preprocessing),
            ("Destination Prediction", self.step2_destination_prediction),
            ("Business Analysis", self.step3_business_analysis),
            ("Visualization", self.step4_visualization)
        ]
        
        for step_name, step_func in steps:
            self.log(f"Starting {step_name}...")
            if not step_func():
                self.log(f"Pipeline failed at {step_name}")
                return False
        
        # 计算总耗时
        total_time = time.time() - self.start_time
        self.log(f"=== Pipeline completed successfully in {total_time:.2f} seconds ===")
        
        # 生成最终报告
        self.generate_final_report()
        
        return True
    
    def generate_final_report(self):
        """生成最终报告"""
        self.log("Generating final report...")
        
        report = f"""
# 摩拜单车商业选址推荐系统分析报告

## 执行时间
- 开始时间: {datetime.fromtimestamp(self.start_time).strftime('%Y-%m-%d %H:%M:%S')}
- 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 总耗时: {time.time() - self.start_time:.2f} 秒

## 生成文件
- processed_train.csv: 处理后的训练数据
- processed_test.csv: 处理后的测试数据
- destination_predictions.csv: 终点预测结果
- destination_model.pkl: 训练好的预测模型
- business_location_analysis.csv: 商业选址分析结果
- top_business_locations.csv: 推荐商业位置
- results/: 可视化结果目录
  - heatmaps/: 热力图
  - recommendations/: 推荐位置图
  - analysis/: 分析图表
  - interactive_map.html: 交互式地图

## 主要发现
1. 基于摩拜单车出行数据，成功预测了测试集的终点位置
2. 整合POI数据和单车流量数据，构建了综合商业选址评价体系
3. 识别出北京市内具有高商业价值的区域
4. 生成了详细的可视化分析报告

## 使用建议
1. 查看 results/summary_report.png 了解整体分析结果
2. 打开 results/recommendations/interactive_map.html 查看交互式推荐地图
3. 参考 top_business_locations.csv 中的推荐位置进行商业选址决策
"""
        
        with open('final_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.log("Final report saved to final_report.md")

def main():
    """主函数"""
    pipeline = MobikeBusinessPipeline()
    
    # 运行完整流程
    success = pipeline.run_full_pipeline()
    
    if success:
        print("\n🎉 Pipeline completed successfully!")
        print("📊 Check the 'results' directory for visualizations")
        print("📋 Read 'final_report.md' for detailed results")
        print("🗺️  Open 'results/recommendations/interactive_map.html' for interactive map")
    else:
        print("\n❌ Pipeline failed. Check the log file for details.")
    
    return success

if __name__ == "__main__":
    main()
