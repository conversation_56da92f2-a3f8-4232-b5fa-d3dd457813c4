"""
摩拜单车商业选址推荐系统 - 终极可视化
整合所有重要的可视化结果，提供最佳的展示效果
"""

import pandas as pd
import numpy as np
import geopandas as gpd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
import folium
from folium.plugins import HeatMap, MarkerCluster, Fullscreen
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-whitegrid')

class UltimateVisualization:
    """终极可视化类 - 整合所有最佳可视化"""
    
    def __init__(self):
        self.results_dir = 'results/ultimate_viz'
        self.grid_gdf = None
        self.poi_data = None
        self.ensure_results_dir()
        
        # 专业配色方案
        self.colors = {
            'business': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'],
            'heatmap_business': ['#ffffcc', '#ffeda0', '#fed976', '#feb24c', '#fd8d3c', '#fc4e2a', '#e31a1c', '#bd0026', '#800026'],
            'heatmap_flow': ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6', '#4292c6', '#2171b5', '#08519c', '#08306b'],
            'heatmap_potential': ['#f7fcf5', '#e5f5e0', '#c7e9c0', '#a1d99b', '#74c476', '#41ab5d', '#238b45', '#006d2c', '#00441b'],
            'comprehensive': ['#440154', '#31688e', '#35b779', '#fde725']
        }
    
    def ensure_results_dir(self):
        """确保结果目录存在"""
        import os
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
    
    def load_all_data(self):
        """加载所有必要数据"""
        print("Loading all data for ultimate visualization...")
        
        try:
            # 加载网格分析结果
            grid_shp_path = 'results/grid_analysis/grid_analysis_results.shp'
            self.grid_gdf = gpd.read_file(grid_shp_path)
            
            # 修复列名
            column_mapping = {
                'total_poi_': 'total_poi_count',
                'business_p': 'business_poi_count',
                'transport_': 'transport_poi_count',
                'education_': 'education_poi_count',
                'medical_po': 'medical_poi_count',
                'bike_flow_': 'bike_flow_density',
                'weighted_f': 'weighted_flow_density',
                'start_dens': 'start_density',
                'end_densit': 'end_density',
                'predicted_': 'predicted_density',
                'test_start': 'test_start_density',
                'business_s': 'business_score',
                'future_pot': 'future_potential_score',
                'comprehens': 'comprehensive_score'
            }
            self.grid_gdf = self.grid_gdf.rename(columns=column_mapping)
            
            print(f"Grid data loaded: {len(self.grid_gdf)} cells")
            
            # 加载POI数据
            self.poi_data = pd.read_csv('北京市POI数据.csv')
            print(f"POI data loaded: {len(self.poi_data)} records")
            
            return True
            
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
    
    def create_master_heatmap_dashboard(self):
        """创建主热力图仪表板"""
        print("Creating master heatmap dashboard...")
        
        fig, axes = plt.subplots(2, 2, figsize=(24, 20))
        fig.suptitle('摩拜单车商业选址热力图仪表板', fontsize=28, fontweight='bold', y=0.95)
        
        # 1. 综合评分热力图
        self.grid_gdf.plot(
            column='comprehensive_score',
            cmap=LinearSegmentedColormap.from_list("comprehensive", self.colors['comprehensive']),
            linewidth=0.02,
            ax=axes[0, 0],
            edgecolor='white',
            alpha=0.8,
            legend=True,
            legend_kwds={'shrink': 0.8, 'label': '综合评分'}
        )
        axes[0, 0].set_title('综合评分热力图', fontsize=18, fontweight='bold', pad=15)
        axes[0, 0].set_xlabel('经度', fontsize=14)
        axes[0, 0].set_ylabel('纬度', fontsize=14)
        axes[0, 0].tick_params(labelsize=10)
        
        # 2. 商业价值热力图
        self.grid_gdf.plot(
            column='business_score',
            cmap=LinearSegmentedColormap.from_list("business", self.colors['heatmap_business']),
            linewidth=0.02,
            ax=axes[0, 1],
            edgecolor='white',
            alpha=0.8,
            legend=True,
            legend_kwds={'shrink': 0.8, 'label': '商业价值评分'}
        )
        axes[0, 1].set_title('商业价值热力图', fontsize=18, fontweight='bold', pad=15)
        axes[0, 1].set_xlabel('经度', fontsize=14)
        axes[0, 1].set_ylabel('纬度', fontsize=14)
        axes[0, 1].tick_params(labelsize=10)
        
        # 3. 未来潜力热力图
        self.grid_gdf.plot(
            column='future_potential_score',
            cmap=LinearSegmentedColormap.from_list("potential", self.colors['heatmap_potential']),
            linewidth=0.02,
            ax=axes[1, 0],
            edgecolor='white',
            alpha=0.8,
            legend=True,
            legend_kwds={'shrink': 0.8, 'label': '未来潜力评分'}
        )
        axes[1, 0].set_title('未来潜力热力图', fontsize=18, fontweight='bold', pad=15)
        axes[1, 0].set_xlabel('经度', fontsize=14)
        axes[1, 0].set_ylabel('纬度', fontsize=14)
        axes[1, 0].tick_params(labelsize=10)
        
        # 4. 加权流量密度热力图
        self.grid_gdf.plot(
            column='weighted_flow_density',
            cmap=LinearSegmentedColormap.from_list("flow", self.colors['heatmap_flow']),
            linewidth=0.02,
            ax=axes[1, 1],
            edgecolor='white',
            alpha=0.8,
            legend=True,
            legend_kwds={'shrink': 0.8, 'label': '加权流量密度'}
        )
        axes[1, 1].set_title('加权流量密度热力图', fontsize=18, fontweight='bold', pad=15)
        axes[1, 1].set_xlabel('经度', fontsize=14)
        axes[1, 1].set_ylabel('纬度', fontsize=14)
        axes[1, 1].tick_params(labelsize=10)
        
        # 美化所有子图
        for ax in axes.flat:
            ax.set_facecolor('#f8f9fa')
            ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)
            for spine in ax.spines.values():
                spine.set_linewidth(1.5)
                spine.set_color('#333333')
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/master_heatmap_dashboard.png', 
                   dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
    
    def create_business_analysis_dashboard(self):
        """创建商业分析仪表板"""
        print("Creating business analysis dashboard...")
        
        # 获取推荐数据
        top_20 = self.grid_gdf.nlargest(20, 'comprehensive_score')
        
        fig, axes = plt.subplots(2, 3, figsize=(24, 16))
        fig.suptitle('商业选址分析仪表板', fontsize=24, fontweight='bold', y=0.95)
        
        # 1. 推荐位置评分对比
        bars = axes[0, 0].bar(range(len(top_20.head(10))), top_20.head(10)['comprehensive_score'], 
                             color=self.colors['business'], alpha=0.8, edgecolor='black', linewidth=1)
        axes[0, 0].set_title('前10推荐位置综合评分', fontsize=16, fontweight='bold')
        axes[0, 0].set_xlabel('推荐排名', fontsize=12)
        axes[0, 0].set_ylabel('综合评分', fontsize=12)
        axes[0, 0].set_xticks(range(10))
        axes[0, 0].set_xticklabels([f'第{i+1}名' for i in range(10)], rotation=45)
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + 1,
                           f'{height:.1f}', ha='center', va='bottom', fontweight='bold')
        
        # 2. POI分布统计
        poi_counts = self.poi_data['大类'].value_counts().head(8)
        wedges, texts, autotexts = axes[0, 1].pie(poi_counts.values, labels=poi_counts.index, 
                                                 autopct='%1.1f%%', startangle=90,
                                                 colors=plt.cm.Set3(np.linspace(0, 1, len(poi_counts))))
        axes[0, 1].set_title('北京市POI分布', fontsize=16, fontweight='bold')
        
        # 3. 评分相关性分析
        score_data = top_20[['business_score', 'future_potential_score', 'comprehensive_score']]
        correlation = score_data.corr()
        im = axes[0, 2].imshow(correlation, cmap='RdYlBu_r', aspect='auto', vmin=-1, vmax=1)
        axes[0, 2].set_xticks(range(len(correlation.columns)))
        axes[0, 2].set_yticks(range(len(correlation.columns)))
        axes[0, 2].set_xticklabels(['商业价值', '未来潜力', '综合评分'], rotation=45)
        axes[0, 2].set_yticklabels(['商业价值', '未来潜力', '综合评分'])
        axes[0, 2].set_title('评分相关性矩阵', fontsize=16, fontweight='bold')
        
        # 添加相关性数值
        for i in range(len(correlation.columns)):
            for j in range(len(correlation.columns)):
                text = axes[0, 2].text(j, i, f'{correlation.iloc[i, j]:.2f}',
                                      ha="center", va="center", color="black", fontweight='bold')
        
        # 4. 推荐位置地理分布
        scatter = axes[1, 0].scatter(top_20['center_lon'], top_20['center_lat'], 
                                    c=top_20['comprehensive_score'], s=top_20['comprehensive_score']*3,
                                    cmap='viridis', alpha=0.7, edgecolors='black', linewidth=1)
        axes[1, 0].set_title('推荐位置地理分布', fontsize=16, fontweight='bold')
        axes[1, 0].set_xlabel('经度', fontsize=12)
        axes[1, 0].set_ylabel('纬度', fontsize=12)
        
        # 标注前5名
        for i, row in top_20.head(5).iterrows():
            axes[1, 0].annotate(f'#{i+1}', (row['center_lon'], row['center_lat']),
                               xytext=(5, 5), textcoords='offset points', fontweight='bold',
                               bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
        
        plt.colorbar(scatter, ax=axes[1, 0], label='综合评分')
        
        # 5. 流量类型分析
        flow_types = ['start_density', 'end_density', 'predicted_density', 'test_start_density']
        flow_labels = ['起点密度', '终点密度', '预测密度', '测试起点密度']
        flow_means = [top_20[col].mean() for col in flow_types]
        
        bars = axes[1, 1].bar(flow_labels, flow_means, 
                             color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
                             alpha=0.8, edgecolor='black', linewidth=1)
        axes[1, 1].set_title('平均流量密度分析', fontsize=16, fontweight='bold')
        axes[1, 1].set_ylabel('平均密度', fontsize=12)
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                           f'{height:.0f}', ha='center', va='bottom', fontweight='bold')
        
        # 6. 评分分布箱线图
        score_data_melted = pd.melt(score_data, var_name='评分类型', value_name='评分值')
        score_data_melted['评分类型'] = score_data_melted['评分类型'].map({
            'business_score': '商业价值',
            'future_potential_score': '未来潜力', 
            'comprehensive_score': '综合评分'
        })
        
        box_plot = axes[1, 2].boxplot([score_data['business_score'], score_data['future_potential_score'], 
                                      score_data['comprehensive_score']], 
                                     labels=['商业价值', '未来潜力', '综合评分'],
                                     patch_artist=True)
        
        colors = ['lightblue', 'lightgreen', 'lightcoral']
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        axes[1, 2].set_title('评分分布箱线图', fontsize=16, fontweight='bold')
        axes[1, 2].set_ylabel('评分值', fontsize=12)
        axes[1, 2].tick_params(axis='x', rotation=45)
        
        # 美化所有子图
        for ax in axes.flat:
            ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)
            for spine in ax.spines.values():
                spine.set_linewidth(1.5)
                spine.set_color('#333333')
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/business_analysis_dashboard.png', 
                   dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
    
    def create_ultimate_interactive_map(self):
        """创建终极交互式地图"""
        print("Creating ultimate interactive map...")
        
        # 计算地图中心
        bounds = self.grid_gdf.total_bounds
        center_lat = (bounds[1] + bounds[3]) / 2
        center_lon = (bounds[0] + bounds[2]) / 2
        
        # 创建地图
        m = folium.Map(
            location=[center_lat, center_lon],
            zoom_start=10,
            tiles=None
        )
        
        # 添加多种地图图层
        folium.TileLayer('OpenStreetMap', name='🗺️ 街道地图').add_to(m)
        folium.TileLayer('CartoDB positron', name='🎨 简洁地图').add_to(m)
        folium.TileLayer('Stamen Terrain', name='🏔️ 地形图').add_to(m)
        
        # 获取不同级别的推荐网格
        top_grids = self.grid_gdf.nlargest(50, 'comprehensive_score')
        high_business = self.grid_gdf[self.grid_gdf['business_score'] > 80]
        high_potential = self.grid_gdf[self.grid_gdf['future_potential_score'] > 80]
        
        # 1. 添加综合评分热力图层
        def add_choropleth_layer(data, column, name, colormap, threshold=None):
            if threshold:
                data = data[data[column] > threshold]
            
            if len(data) > 0:
                # 创建分级着色图
                folium.Choropleth(
                    geo_data=data,
                    data=data,
                    columns=['grid_id', column],
                    key_on='feature.properties.grid_id',
                    fill_color=colormap,
                    fill_opacity=0.7,
                    line_opacity=0.2,
                    legend_name=name,
                    name=name
                ).add_to(m)
        
        # 添加不同评分的热力图层
        add_choropleth_layer(self.grid_gdf, 'comprehensive_score', '🎯 综合评分热力图', 'YlOrRd', 60)
        add_choropleth_layer(high_business, 'business_score', '💼 商业价值热力图', 'Reds')
        add_choropleth_layer(high_potential, 'future_potential_score', '🚀 未来潜力热力图', 'Blues')
        
        # 2. 添加推荐位置标记群组
        recommendation_cluster = MarkerCluster(name='⭐ 推荐位置').add_to(m)
        
        for idx, row in top_grids.head(20).iterrows():
            # 根据评分选择图标和颜色
            if row['comprehensive_score'] > 95:
                color, icon = 'red', 'star'
                rank_text = '🥇 顶级推荐'
            elif row['comprehensive_score'] > 85:
                color, icon = 'orange', 'heart'
                rank_text = '🥈 优质推荐'
            else:
                color, icon = 'green', 'info-sign'
                rank_text = '🥉 良好推荐'
            
            folium.Marker(
                location=[row['center_lat'], row['center_lon']],
                popup=folium.Popup(f"""
                <div style="width:280px; font-family: Arial;">
                <h3 style="color: #2E86AB; margin-bottom: 10px;">
                    {rank_text} - 网格 #{row['grid_id']}
                </h3>
                <hr style="margin: 10px 0;">
                <table style="width: 100%; font-size: 12px;">
                    <tr><td><b>🎯 综合评分:</b></td><td style="color: #E76F51;"><b>{row['comprehensive_score']:.1f}</b></td></tr>
                    <tr><td><b>💼 商业价值:</b></td><td>{row['business_score']:.1f}</td></tr>
                    <tr><td><b>🚀 未来潜力:</b></td><td>{row['future_potential_score']:.1f}</td></tr>
                    <tr><td><b>🏢 POI总数:</b></td><td>{row['total_poi_count']:.0f}</td></tr>
                    <tr><td><b>🛍️ 商业POI:</b></td><td>{row['business_poi_count']:.0f}</td></tr>
                    <tr><td><b>🚲 加权流量:</b></td><td>{row['weighted_flow_density']:.0f}</td></tr>
                    <tr><td><b>📍 预测密度:</b></td><td>{row['predicted_density']:.0f}</td></tr>
                </table>
                <hr style="margin: 10px 0;">
                <p style="font-size: 11px; color: #666;">
                    <b>📍 坐标:</b> ({row['center_lat']:.4f}, {row['center_lon']:.4f})
                </p>
                </div>
                """, max_width=320),
                tooltip=f"{rank_text} - 评分:{row['comprehensive_score']:.1f}",
                icon=folium.Icon(color=color, icon=icon, prefix='fa')
            ).add_to(recommendation_cluster)
        
        # 3. 添加流量热力图
        if len(self.grid_gdf) > 0:
            # 采样高流量网格用于热力图
            high_flow_grids = self.grid_gdf[self.grid_gdf['weighted_flow_density'] > 
                                          self.grid_gdf['weighted_flow_density'].quantile(0.8)]
            
            if len(high_flow_grids) > 0:
                heat_data = []
                for _, row in high_flow_grids.iterrows():
                    heat_data.append([
                        row['center_lat'],
                        row['center_lon'],
                        row['weighted_flow_density']
                    ])
                
                HeatMap(
                    heat_data,
                    name='🔥 流量热力图',
                    radius=15,
                    blur=10,
                    max_zoom=1,
                    gradient={0.2: 'blue', 0.4: 'lime', 0.6: 'orange', 1: 'red'}
                ).add_to(m)
        
        # 4. 添加控制组件
        folium.LayerControl(position='topright').add_to(m)
        Fullscreen(position='topleft').add_to(m)
        
        # 5. 添加图例
        legend_html = '''
        <div style="position: fixed; 
                    bottom: 50px; left: 50px; width: 200px; height: 120px; 
                    background-color: white; border:2px solid grey; z-index:9999; 
                    font-size:14px; padding: 10px">
        <h4 style="margin-top:0;">📊 评分图例</h4>
        <p><span style="color:red;">🥇</span> 顶级推荐 (95+)</p>
        <p><span style="color:orange;">🥈</span> 优质推荐 (85-95)</p>
        <p><span style="color:green;">🥉</span> 良好推荐 (70-85)</p>
        </div>
        '''
        m.get_root().html.add_child(folium.Element(legend_html))
        
        # 保存地图
        m.save(f'{self.results_dir}/ultimate_interactive_map.html')
        print(f"Ultimate interactive map saved to {self.results_dir}/ultimate_interactive_map.html")
        
        return m
    
    def create_plotly_comprehensive_dashboard(self):
        """创建Plotly综合仪表板"""
        print("Creating Plotly comprehensive dashboard...")
        
        top_20 = self.grid_gdf.nlargest(20, 'comprehensive_score')
        
        # 创建子图
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=[
                '🎯 综合评分分布', '📍 推荐位置地理分布',
                '💼 商业价值 vs 🚀 未来潜力', '🚲 流量密度分析',
                '📊 评分对比雷达图', '🏢 POI密度统计'
            ],
            specs=[
                [{"type": "histogram"}, {"type": "scatter"}],
                [{"type": "scatter"}, {"type": "bar"}],
                [{"type": "scatterpolar"}, {"type": "box"}]
            ]
        )
        
        # 1. 综合评分分布
        fig.add_trace(
            go.Histogram(
                x=self.grid_gdf['comprehensive_score'],
                nbinsx=50,
                name='评分分布',
                marker_color='skyblue',
                opacity=0.7
            ),
            row=1, col=1
        )
        
        # 2. 推荐位置地理分布
        fig.add_trace(
            go.Scatter(
                x=top_20['center_lon'],
                y=top_20['center_lat'],
                mode='markers+text',
                marker=dict(
                    size=top_20['comprehensive_score']/3,
                    color=top_20['comprehensive_score'],
                    colorscale='Viridis',
                    showscale=True,
                    colorbar=dict(title="综合评分", x=0.48)
                ),
                text=[f'#{i+1}' for i in range(len(top_20))],
                textposition="middle center",
                name='推荐位置',
                hovertemplate='网格%{text}<br>经度: %{x}<br>纬度: %{y}<br>评分: %{marker.color:.1f}<extra></extra>'
            ),
            row=1, col=2
        )
        
        # 3. 商业价值 vs 未来潜力
        fig.add_trace(
            go.Scatter(
                x=top_20['business_score'],
                y=top_20['future_potential_score'],
                mode='markers',
                marker=dict(
                    size=top_20['comprehensive_score']/4,
                    color=top_20['comprehensive_score'],
                    colorscale='RdYlBu_r',
                    line=dict(width=2, color='white')
                ),
                text=[f'网格{gid}' for gid in top_20['grid_id']],
                name='评分关系',
                hovertemplate='%{text}<br>商业价值: %{x:.1f}<br>未来潜力: %{y:.1f}<extra></extra>'
            ),
            row=2, col=1
        )
        
        # 4. 流量密度分析
        flow_types = ['start_density', 'end_density', 'predicted_density', 'test_start_density']
        flow_labels = ['起点密度', '终点密度', '预测密度', '测试起点密度']
        flow_means = [top_20[col].mean() for col in flow_types]
        
        fig.add_trace(
            go.Bar(
                x=flow_labels,
                y=flow_means,
                marker_color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
                name='流量分析',
                text=[f'{val:.0f}' for val in flow_means],
                textposition='auto'
            ),
            row=2, col=2
        )
        
        # 5. 评分对比雷达图
        top_5 = top_20.head(5)
        for i, (_, row) in enumerate(top_5.iterrows()):
            fig.add_trace(
                go.Scatterpolar(
                    r=[row['business_score'], row['future_potential_score'], 
                       row['total_poi_count']/20, row['weighted_flow_density']/50],
                    theta=['商业价值', '未来潜力', 'POI密度', '流量密度'],
                    fill='toself',
                    name=f'网格{row["grid_id"]}',
                    opacity=0.6
                ),
                row=3, col=1
            )
        
        # 6. POI密度统计
        poi_categories = ['total_poi_count', 'business_poi_count', 'transport_poi_count']
        poi_labels = ['总POI', '商业POI', '交通POI']
        
        for cat, label in zip(poi_categories, poi_labels):
            fig.add_trace(
                go.Box(
                    y=top_20[cat],
                    name=label,
                    boxpoints='outliers'
                ),
                row=3, col=2
            )
        
        # 更新布局
        fig.update_layout(
            title={
                'text': '🎯 摩拜单车商业选址综合分析仪表板',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 24, 'family': 'Arial Black'}
            },
            height=1200,
            showlegend=True,
            template='plotly_white'
        )
        
        # 保存仪表板
        fig.write_html(f'{self.results_dir}/plotly_comprehensive_dashboard.html')
        print(f"Plotly dashboard saved to {self.results_dir}/plotly_comprehensive_dashboard.html")
        
        return fig
    
    def generate_final_summary(self):
        """生成最终总结报告"""
        print("Generating final summary...")
        
        top_10 = self.grid_gdf.nlargest(10, 'comprehensive_score')
        
        summary = f"""
# 🎯 摩拜单车商业选址推荐系统 - 终极分析报告

## 📊 系统概览
- **分析网格总数**: {len(self.grid_gdf):,} 个
- **POI数据点**: {len(self.poi_data):,} 个
- **推荐位置**: {len(top_10)} 个顶级选址

## 🏆 顶级推荐位置

| 🥇 排名 | 网格ID | 📍 中心坐标 | 🎯 综合评分 | 💼 商业价值 | 🚀 未来潜力 | 🏢 POI总数 | 🚲 加权流量 |
|---------|--------|-------------|-------------|-------------|-------------|------------|------------|
"""
        
        for i, (_, row) in enumerate(top_10.iterrows()):
            emoji = '🥇' if i == 0 else '🥈' if i == 1 else '🥉' if i == 2 else '⭐'
            summary += f"| {emoji} {i+1} | {row['grid_id']} | ({row['center_lat']:.4f}, {row['center_lon']:.4f}) | {row['comprehensive_score']:.1f} | {row['business_score']:.1f} | {row['future_potential_score']:.1f} | {row['total_poi_count']:.0f} | {row['weighted_flow_density']:.0f} |\n"
        
        summary += f"""

## 🔍 关键发现

### 📈 评分统计
- **最高综合评分**: {top_10['comprehensive_score'].max():.1f} 分
- **平均综合评分**: {top_10['comprehensive_score'].mean():.1f} 分
- **评分标准差**: {top_10['comprehensive_score'].std():.1f} 分

### 🚲 流量特征
- **平均加权流量**: {top_10['weighted_flow_density'].mean():.0f}
- **最高流量网格**: 网格 {top_10.loc[top_10['weighted_flow_density'].idxmax(), 'grid_id']} ({top_10['weighted_flow_density'].max():.0f})
- **预测流量占比**: {(top_10['predicted_density'].sum() / top_10['weighted_flow_density'].sum() * 100):.1f}%

### 🏢 POI分布
- **平均POI密度**: {top_10['total_poi_count'].mean():.0f} 个/网格
- **商业POI占比**: {(top_10['business_poi_count'].sum() / top_10['total_poi_count'].sum() * 100):.1f}%
- **最高POI密度**: {top_10['total_poi_count'].max():.0f} 个

## 💼 投资建议

### 🎯 优先级分类
- **🥇 A级推荐** (综合评分>95): {len(top_10[top_10['comprehensive_score'] > 95])} 个网格 - 立即投资
- **🥈 B级推荐** (综合评分85-95): {len(top_10[(top_10['comprehensive_score'] >= 85) & (top_10['comprehensive_score'] <= 95)])} 个网格 - 优先考虑
- **🥉 C级推荐** (综合评分75-85): {len(top_10[(top_10['comprehensive_score'] >= 75) & (top_10['comprehensive_score'] < 85)])} 个网格 - 稳健选择

### 💡 策略建议
1. **短期收益**: 重点关注商业价值评分>90的网格
2. **长期布局**: 投资未来潜力评分>80的网格
3. **平衡投资**: 选择综合评分最高的网格

## 📁 可视化文件

### 🖼️ 静态图表
- `master_heatmap_dashboard.png` - 主热力图仪表板
- `business_analysis_dashboard.png` - 商业分析仪表板

### 🌐 交互式可视化
- `ultimate_interactive_map.html` - 终极交互式地图
- `plotly_comprehensive_dashboard.html` - Plotly综合仪表板

## 🎉 系统价值

这个基于网格shp文件的商业选址推荐系统成功整合了：
- ✅ 摩拜单车真实出行数据
- ✅ 机器学习终点预测
- ✅ 北京市POI分布数据
- ✅ 标准地理网格分析
- ✅ 多维度评分体系
- ✅ 专业级可视化展示

为商业选址决策提供了科学、准确、直观的数据支撑！
"""
        
        # 保存总结报告
        with open(f'{self.results_dir}/ultimate_summary_report.md', 'w', encoding='utf-8') as f:
            f.write(summary)
        
        print(f"Final summary saved to {self.results_dir}/ultimate_summary_report.md")

def main():
    """主函数"""
    print("🚀 Starting Ultimate Visualization System...")
    
    # 创建终极可视化对象
    viz = UltimateVisualization()
    
    # 加载所有数据
    if not viz.load_all_data():
        print("❌ Failed to load data")
        return
    
    print("✅ Data loaded successfully!")
    
    # 创建所有可视化
    print("\n📊 Creating visualizations...")
    
    # 1. 主热力图仪表板
    viz.create_master_heatmap_dashboard()
    
    # 2. 商业分析仪表板
    viz.create_business_analysis_dashboard()
    
    # 3. 终极交互式地图
    viz.create_ultimate_interactive_map()
    
    # 4. Plotly综合仪表板
    viz.create_plotly_comprehensive_dashboard()
    
    # 5. 生成最终总结
    viz.generate_final_summary()
    
    print(f"\n🎉 All visualizations completed!")
    print(f"📁 Results saved in: {viz.results_dir}")
    print(f"🌐 Open the interactive files to explore the results!")

if __name__ == "__main__":
    main()
