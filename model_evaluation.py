import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import train_test_split
import joblib
from destination_prediction import DestinationPredictor
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ModelEvaluator:
    """模型评估类"""
    
    def __init__(self):
        self.predictor = None
        self.results_dir = 'results/model_evaluation'
        self.ensure_results_dir()
    
    def ensure_results_dir(self):
        """确保结果目录存在"""
        import os
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
    
    def calculate_distance_error(self, y_true_lat, y_true_lon, y_pred_lat, y_pred_lon):
        """计算预测位置与真实位置的距离误差（公里）"""
        from math import radians, cos, sin, asin, sqrt
        
        distances = []
        for i in range(len(y_true_lat)):
            lat1, lon1 = radians(y_true_lat.iloc[i]), radians(y_true_lon.iloc[i])
            lat2, lon2 = radians(y_pred_lat[i]), radians(y_pred_lon[i])
            
            dlat = lat2 - lat1
            dlon = lon2 - lon1
            a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
            c = 2 * asin(sqrt(a))
            r = 6371  # 地球半径（公里）
            
            distances.append(c * r)
        
        return np.array(distances)
    
    def load_model_and_data(self):
        """加载模型和数据"""
        print("Loading model and data...")
        
        # 加载训练好的模型
        try:
            self.predictor = DestinationPredictor()
            self.predictor.load_model('destination_model.pkl')
            print("Model loaded successfully")
        except FileNotFoundError:
            print("Model not found. Please train the model first.")
            return False
        
        # 加载处理后的训练数据
        try:
            self.train_data = pd.read_csv('processed_train.csv')
            print(f"Training data loaded: {self.train_data.shape}")
        except FileNotFoundError:
            print("Processed training data not found.")
            return False
        
        return True
    
    def evaluate_model_performance(self, sample_size=50000):
        """评估模型性能"""
        print(f"Evaluating model performance with {sample_size} samples...")
        
        # 采样数据以加快评估速度
        if len(self.train_data) > sample_size:
            eval_data = self.train_data.sample(n=sample_size, random_state=42)
        else:
            eval_data = self.train_data.copy()
        
        # 分割数据
        X = self.predictor.prepare_features(eval_data)
        y_lat = eval_data['geohashed_end_loc_lat']
        y_lon = eval_data['geohashed_end_loc_lon']
        
        X_train, X_test, y_lat_train, y_lat_test, y_lon_train, y_lon_test = train_test_split(
            X, y_lat, y_lon, test_size=0.3, random_state=42
        )
        
        # 标准化特征
        X_test_scaled = self.predictor.scaler.transform(X_test)
        
        # 预测
        lat_pred = self.predictor.lat_model.predict(X_test_scaled)
        lon_pred = self.predictor.lon_model.predict(X_test_scaled)
        
        # 计算各种评估指标
        metrics = {}
        
        # 纬度指标
        metrics['lat_mse'] = mean_squared_error(y_lat_test, lat_pred)
        metrics['lat_mae'] = mean_absolute_error(y_lat_test, lat_pred)
        metrics['lat_rmse'] = np.sqrt(metrics['lat_mse'])
        metrics['lat_r2'] = r2_score(y_lat_test, lat_pred)
        
        # 经度指标
        metrics['lon_mse'] = mean_squared_error(y_lon_test, lon_pred)
        metrics['lon_mae'] = mean_absolute_error(y_lon_test, lon_pred)
        metrics['lon_rmse'] = np.sqrt(metrics['lon_mse'])
        metrics['lon_r2'] = r2_score(y_lon_test, lon_pred)
        
        # 距离误差
        distance_errors = self.calculate_distance_error(y_lat_test, y_lon_test, lat_pred, lon_pred)
        metrics['mean_distance_error'] = np.mean(distance_errors)
        metrics['median_distance_error'] = np.median(distance_errors)
        metrics['distance_error_std'] = np.std(distance_errors)
        
        # 准确度指标（在不同距离阈值内的预测比例）
        for threshold in [0.5, 1.0, 2.0, 5.0]:
            accuracy = np.mean(distance_errors <= threshold)
            metrics[f'accuracy_{threshold}km'] = accuracy
        
        return metrics, y_lat_test, y_lon_test, lat_pred, lon_pred, distance_errors
    
    def plot_prediction_accuracy(self, y_lat_test, y_lon_test, lat_pred, lon_pred, distance_errors):
        """绘制预测准确度图表"""
        print("Creating prediction accuracy plots...")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. 纬度预测 vs 真实值
        axes[0, 0].scatter(y_lat_test, lat_pred, alpha=0.5, s=1)
        axes[0, 0].plot([y_lat_test.min(), y_lat_test.max()], [y_lat_test.min(), y_lat_test.max()], 'r--', lw=2)
        axes[0, 0].set_xlabel('真实纬度')
        axes[0, 0].set_ylabel('预测纬度')
        axes[0, 0].set_title('纬度预测准确度')
        
        # 2. 经度预测 vs 真实值
        axes[0, 1].scatter(y_lon_test, lon_pred, alpha=0.5, s=1)
        axes[0, 1].plot([y_lon_test.min(), y_lon_test.max()], [y_lon_test.min(), y_lon_test.max()], 'r--', lw=2)
        axes[0, 1].set_xlabel('真实经度')
        axes[0, 1].set_ylabel('预测经度')
        axes[0, 1].set_title('经度预测准确度')
        
        # 3. 距离误差分布
        axes[0, 2].hist(distance_errors, bins=50, alpha=0.7, edgecolor='black')
        axes[0, 2].axvline(np.mean(distance_errors), color='red', linestyle='--', label=f'平均误差: {np.mean(distance_errors):.2f}km')
        axes[0, 2].axvline(np.median(distance_errors), color='green', linestyle='--', label=f'中位数误差: {np.median(distance_errors):.2f}km')
        axes[0, 2].set_xlabel('距离误差 (km)')
        axes[0, 2].set_ylabel('频次')
        axes[0, 2].set_title('距离误差分布')
        axes[0, 2].legend()
        
        # 4. 纬度误差分布
        lat_errors = np.abs(y_lat_test - lat_pred)
        axes[1, 0].hist(lat_errors, bins=50, alpha=0.7, color='orange', edgecolor='black')
        axes[1, 0].set_xlabel('纬度绝对误差')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].set_title('纬度误差分布')
        
        # 5. 经度误差分布
        lon_errors = np.abs(y_lon_test - lon_pred)
        axes[1, 1].hist(lon_errors, bins=50, alpha=0.7, color='green', edgecolor='black')
        axes[1, 1].set_xlabel('经度绝对误差')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].set_title('经度误差分布')
        
        # 6. 累积准确度曲线
        sorted_errors = np.sort(distance_errors)
        cumulative_accuracy = np.arange(1, len(sorted_errors) + 1) / len(sorted_errors)
        axes[1, 2].plot(sorted_errors, cumulative_accuracy, linewidth=2)
        axes[1, 2].set_xlabel('距离误差阈值 (km)')
        axes[1, 2].set_ylabel('累积准确度')
        axes[1, 2].set_title('累积准确度曲线')
        axes[1, 2].grid(True, alpha=0.3)
        
        # 添加关键阈值线
        for threshold in [0.5, 1.0, 2.0, 5.0]:
            accuracy = np.mean(distance_errors <= threshold)
            axes[1, 2].axvline(threshold, color='red', linestyle='--', alpha=0.7)
            axes[1, 2].text(threshold, accuracy, f'{threshold}km\n{accuracy:.1%}', 
                           ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/prediction_accuracy.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_evaluation_report(self, metrics):
        """生成评估报告"""
        print("\n" + "="*60)
        print("摩拜单车终点预测模型评估报告")
        print("="*60)
        
        print(f"\n基础统计指标:")
        print(f"纬度预测:")
        print(f"  - 均方误差 (MSE): {metrics['lat_mse']:.8f}")
        print(f"  - 平均绝对误差 (MAE): {metrics['lat_mae']:.6f}")
        print(f"  - 均方根误差 (RMSE): {metrics['lat_rmse']:.6f}")
        print(f"  - R² 决定系数: {metrics['lat_r2']:.6f}")
        
        print(f"\n经度预测:")
        print(f"  - 均方误差 (MSE): {metrics['lon_mse']:.8f}")
        print(f"  - 平均绝对误差 (MAE): {metrics['lon_mae']:.6f}")
        print(f"  - 均方根误差 (RMSE): {metrics['lon_rmse']:.6f}")
        print(f"  - R² 决定系数: {metrics['lon_r2']:.6f}")
        
        print(f"\n距离误差分析:")
        print(f"  - 平均距离误差: {metrics['mean_distance_error']:.3f} km")
        print(f"  - 中位数距离误差: {metrics['median_distance_error']:.3f} km")
        print(f"  - 距离误差标准差: {metrics['distance_error_std']:.3f} km")
        
        print(f"\n准确度指标 (在指定距离内的预测比例):")
        for threshold in [0.5, 1.0, 2.0, 5.0]:
            accuracy = metrics[f'accuracy_{threshold}km']
            print(f"  - {threshold}km 内准确度: {accuracy:.1%}")
        
        print(f"\n模型性能评价:")
        
        # 根据R²评价模型质量
        avg_r2 = (metrics['lat_r2'] + metrics['lon_r2']) / 2
        if avg_r2 > 0.9:
            quality = "优秀"
        elif avg_r2 > 0.8:
            quality = "良好"
        elif avg_r2 > 0.7:
            quality = "中等"
        else:
            quality = "需要改进"
        
        print(f"  - 整体模型质量: {quality} (平均R² = {avg_r2:.3f})")
        
        # 根据距离误差评价实用性
        if metrics['mean_distance_error'] < 1.0:
            practicality = "高实用性"
        elif metrics['mean_distance_error'] < 2.0:
            practicality = "中等实用性"
        else:
            practicality = "实用性有限"
        
        print(f"  - 实际应用价值: {practicality} (平均误差 {metrics['mean_distance_error']:.2f}km)")
        
        print(f"\n建议:")
        if metrics['accuracy_1.0km'] > 0.8:
            print("  - 模型预测精度较高，适合用于商业选址分析")
        elif metrics['accuracy_2.0km'] > 0.8:
            print("  - 模型预测精度中等，可用于区域级别的分析")
        else:
            print("  - 建议进一步优化模型，增加更多特征或调整算法参数")
        
        if metrics['lat_r2'] < metrics['lon_r2']:
            print("  - 纬度预测相对较弱，可考虑针对性优化")
        elif metrics['lon_r2'] < metrics['lat_r2']:
            print("  - 经度预测相对较弱，可考虑针对性优化")
        
        # 保存报告到文件
        with open(f'{self.results_dir}/evaluation_report.txt', 'w', encoding='utf-8') as f:
            f.write("摩拜单车终点预测模型评估报告\n")
            f.write("="*60 + "\n\n")
            for key, value in metrics.items():
                if isinstance(value, float):
                    f.write(f"{key}: {value:.6f}\n")
                else:
                    f.write(f"{key}: {value}\n")
        
        print(f"\n详细报告已保存至: {self.results_dir}/evaluation_report.txt")

def main():
    """主函数"""
    evaluator = ModelEvaluator()
    
    # 加载模型和数据
    if not evaluator.load_model_and_data():
        return
    
    # 评估模型性能
    metrics, y_lat_test, y_lon_test, lat_pred, lon_pred, distance_errors = evaluator.evaluate_model_performance()
    
    # 生成可视化图表
    evaluator.plot_prediction_accuracy(y_lat_test, y_lon_test, lat_pred, lon_pred, distance_errors)
    
    # 生成评估报告
    evaluator.generate_evaluation_report(metrics)
    
    return metrics

if __name__ == "__main__":
    metrics = main()
