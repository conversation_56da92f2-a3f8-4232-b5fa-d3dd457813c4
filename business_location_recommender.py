import pandas as pd
import numpy as np
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

class BusinessLocationRecommender:
    """商业选址推荐系统"""
    
    def __init__(self):
        self.poi_data = None
        self.bike_flow_data = None
        self.location_scores = None
        self.grid_size = 0.01  # 约1公里网格，减少计算量
        
    def load_data(self):
        """加载数据"""
        print("Loading data for business location analysis...")
        self.poi_data = pd.read_csv('北京市POI数据.csv')
        
        # 加载摩拜单车流量数据
        try:
            predictions = pd.read_csv('destination_predictions.csv')
            train_data = pd.read_csv('processed_train.csv')
            
            # 采样数据以减少计算量
            sample_size = min(100000, len(train_data))
            train_sample = train_data.sample(n=sample_size, random_state=42)

            # 合并起点和终点数据
            start_points = train_sample[['geohashed_start_loc_lat', 'geohashed_start_loc_lon']].copy()
            start_points.columns = ['lat', 'lon']
            start_points['type'] = 'start'

            end_points = train_sample[['geohashed_end_loc_lat', 'geohashed_end_loc_lon']].copy()
            end_points.columns = ['lat', 'lon']
            end_points['type'] = 'end'

            # 采样预测数据
            pred_sample_size = min(50000, len(predictions))
            pred_sample = predictions.sample(n=pred_sample_size, random_state=42)
            pred_points = pred_sample[['predicted_lat', 'predicted_lon']].copy()
            pred_points.columns = ['lat', 'lon']
            pred_points['type'] = 'predicted'

            self.bike_flow_data = pd.concat([start_points, end_points, pred_points], ignore_index=True)
            print(f"Bike flow data shape: {self.bike_flow_data.shape}")
            
        except FileNotFoundError:
            print("Prediction file not found, using only POI data")
            self.bike_flow_data = None
    
    def create_grid(self, lat_min, lat_max, lon_min, lon_max):
        """创建网格系统"""
        lat_range = np.arange(lat_min, lat_max, self.grid_size)
        lon_range = np.arange(lon_min, lon_max, self.grid_size)
        
        grid_points = []
        for lat in lat_range:
            for lon in lon_range:
                grid_points.append({
                    'grid_lat': lat + self.grid_size/2,
                    'grid_lon': lon + self.grid_size/2,
                    'lat_min': lat,
                    'lat_max': lat + self.grid_size,
                    'lon_min': lon,
                    'lon_max': lon + self.grid_size
                })
        
        return pd.DataFrame(grid_points)
    
    def calculate_poi_density(self, grid_df):
        """计算POI密度"""
        print("Calculating POI density...")

        # 初始化计数列
        grid_df['total_poi_count'] = 0
        grid_df['business_poi_count'] = 0
        grid_df['transport_poi_count'] = 0
        grid_df['education_poi_count'] = 0
        grid_df['medical_poi_count'] = 0

        # 定义类别映射
        category_mapping = {
            'business': ['购物服务', '餐饮服务', '生活服务', '金融保险服务', '公司企业'],
            'transport': ['交通设施'],
            'education': ['科教文化服务'],
            'medical': ['医疗保健服务']
        }

        # 使用向量化操作计算POI密度
        print(f"Processing {len(grid_df)} grid cells...")
        for idx, grid in grid_df.iterrows():
            if idx % 1000 == 0:
                print(f"Processed {idx}/{len(grid_df)} cells")

            # 筛选网格内的POI
            poi_mask = (
                (self.poi_data['纬度'] >= grid['lat_min']) &
                (self.poi_data['纬度'] < grid['lat_max']) &
                (self.poi_data['经度'] >= grid['lon_min']) &
                (self.poi_data['经度'] < grid['lon_max'])
            )
            poi_in_grid = self.poi_data[poi_mask]

            # 计算总POI数量
            grid_df.loc[idx, 'total_poi_count'] = len(poi_in_grid)

            # 计算各类POI数量
            if len(poi_in_grid) > 0:
                poi_categories = poi_in_grid['大类'].value_counts()

                for category_type, categories in category_mapping.items():
                    count = sum(poi_categories.get(cat, 0) for cat in categories)
                    grid_df.loc[idx, f'{category_type}_poi_count'] = count

        print("POI density calculation completed")
        return grid_df
    
    def calculate_bike_flow_density(self, grid_df):
        """计算摩拜单车流量密度"""
        if self.bike_flow_data is None:
            print("No bike flow data available")
            grid_df['bike_flow_density'] = 0
            grid_df['start_density'] = 0
            grid_df['end_density'] = 0
            grid_df['predicted_density'] = 0
            return grid_df

        print("Calculating bike flow density...")

        # 初始化计数列
        grid_df['bike_flow_density'] = 0
        grid_df['start_density'] = 0
        grid_df['end_density'] = 0
        grid_df['predicted_density'] = 0

        for idx, grid in grid_df.iterrows():
            if idx % 1000 == 0:
                print(f"Processed {idx}/{len(grid_df)} cells for bike flow")

            # 筛选网格内的单车流量点
            flow_mask = (
                (self.bike_flow_data['lat'] >= grid['lat_min']) &
                (self.bike_flow_data['lat'] < grid['lat_max']) &
                (self.bike_flow_data['lon'] >= grid['lon_min']) &
                (self.bike_flow_data['lon'] < grid['lon_max'])
            )
            flow_in_grid = self.bike_flow_data[flow_mask]

            grid_df.loc[idx, 'bike_flow_density'] = len(flow_in_grid)

            # 分别计算起点、终点、预测点密度
            if len(flow_in_grid) > 0:
                type_counts = flow_in_grid['type'].value_counts()
                grid_df.loc[idx, 'start_density'] = type_counts.get('start', 0)
                grid_df.loc[idx, 'end_density'] = type_counts.get('end', 0)
                grid_df.loc[idx, 'predicted_density'] = type_counts.get('predicted', 0)

        print("Bike flow density calculation completed")
        return grid_df
    
    def calculate_business_score(self, grid_df):
        """计算商业价值评分"""
        print("Calculating business scores...")
        
        # 标准化各项指标
        scaler = StandardScaler()
        
        # 选择评分指标
        score_features = [
            'total_poi_count', 'business_poi_count', 'transport_poi_count',
            'education_poi_count', 'medical_poi_count', 'bike_flow_density'
        ]
        
        # 确保所有特征列存在
        for feature in score_features:
            if feature not in grid_df.columns:
                grid_df[feature] = 0
        
        # 标准化特征
        scaled_features = scaler.fit_transform(grid_df[score_features])
        scaled_df = pd.DataFrame(scaled_features, columns=score_features)
        
        # 计算加权评分
        weights = {
            'total_poi_count': 0.15,
            'business_poi_count': 0.25,
            'transport_poi_count': 0.15,
            'education_poi_count': 0.10,
            'medical_poi_count': 0.10,
            'bike_flow_density': 0.25
        }
        
        grid_df['business_score'] = 0
        for feature, weight in weights.items():
            grid_df['business_score'] += scaled_df[feature] * weight
        
        # 归一化到0-100分
        min_score = grid_df['business_score'].min()
        max_score = grid_df['business_score'].max()
        grid_df['business_score'] = ((grid_df['business_score'] - min_score) / (max_score - min_score)) * 100
        
        return grid_df
    
    def get_top_recommendations(self, grid_df, top_n=20):
        """获取顶级推荐位置"""
        # 过滤掉评分过低的区域
        filtered_grid = grid_df[grid_df['business_score'] > 50].copy()
        
        # 按评分排序
        top_locations = filtered_grid.nlargest(top_n, 'business_score')
        
        return top_locations
    
    def analyze_business_locations(self):
        """分析商业选址"""
        print("Starting business location analysis...")
        
        # 确定分析范围（北京市主要区域）
        lat_min, lat_max = 39.4, 40.5
        lon_min, lon_max = 115.7, 117.4
        
        # 创建网格
        grid_df = self.create_grid(lat_min, lat_max, lon_min, lon_max)
        print(f"Created grid with {len(grid_df)} cells")
        
        # 计算POI密度
        grid_df = self.calculate_poi_density(grid_df)
        
        # 计算单车流量密度
        grid_df = self.calculate_bike_flow_density(grid_df)
        
        # 计算商业评分
        grid_df = self.calculate_business_score(grid_df)
        
        # 保存结果
        grid_df.to_csv('business_location_analysis.csv', index=False)
        print("Analysis results saved to business_location_analysis.csv")
        
        # 获取推荐位置
        recommendations = self.get_top_recommendations(grid_df)
        recommendations.to_csv('top_business_locations.csv', index=False)
        print("Top recommendations saved to top_business_locations.csv")
        
        self.location_scores = grid_df
        return grid_df, recommendations
    
    def generate_report(self, recommendations):
        """生成推荐报告"""
        print("\n=== 商业选址推荐报告 ===")
        print(f"分析了 {len(self.location_scores)} 个网格区域")
        print(f"推荐了 {len(recommendations)} 个优质商业选址")
        
        print("\n前10个推荐位置:")
        for idx, row in recommendations.head(10).iterrows():
            print(f"位置 {idx+1}: 纬度 {row['grid_lat']:.4f}, 经度 {row['grid_lon']:.4f}")
            print(f"  商业评分: {row['business_score']:.2f}")
            print(f"  POI总数: {row['total_poi_count']:.0f}")
            print(f"  商业POI: {row['business_poi_count']:.0f}")
            print(f"  单车流量: {row['bike_flow_density']:.0f}")
            print()

def main():
    """主函数"""
    recommender = BusinessLocationRecommender()
    recommender.load_data()
    grid_df, recommendations = recommender.analyze_business_locations()
    recommender.generate_report(recommendations)
    
    return recommender, grid_df, recommendations

if __name__ == "__main__":
    recommender, grid_df, recommendations = main()
