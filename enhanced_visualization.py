import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap, ListedColormap
import folium
from folium.plugins import HeatMap, MarkerCluster
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-darkgrid')

class EnhancedVisualization:
    """增强版可视化类"""
    
    def __init__(self):
        self.results_dir = 'results/enhanced_viz'
        self.ensure_results_dir()
        
        # 定义美观的颜色方案
        self.color_schemes = {
            'business': ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D'],
            'flow': ['#0B132B', '#1C2541', '#3A506B', '#5BC0BE', '#6FFFE9'],
            'potential': ['#264653', '#2A9D8F', '#E9C46A', '#F4A261', '#E76F51'],
            'comprehensive': ['#03045E', '#023E8A', '#0077B6', '#0096C7', '#00B4D8', '#48CAE4']
        }
    
    def ensure_results_dir(self):
        """确保结果目录存在"""
        import os
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
    
    def create_beautiful_heatmap(self, grid_df, value_col, title, filename, color_scheme='business'):
        """创建美观的热力图"""
        print(f"Creating beautiful heatmap for {value_col}...")
        
        # 创建透视表
        pivot_data = grid_df.pivot_table(
            values=value_col,
            index='grid_lat',
            columns='grid_lon',
            aggfunc='mean'
        )
        
        # 设置图形大小和样式
        fig, ax = plt.subplots(figsize=(16, 12))
        
        # 创建自定义颜色映射
        colors = self.color_schemes[color_scheme]
        n_colors = len(colors)
        cmap = LinearSegmentedColormap.from_list("custom", colors, N=256)
        
        # 绘制热力图
        im = ax.imshow(
            pivot_data.values,
            cmap=cmap,
            aspect='auto',
            interpolation='bilinear',
            alpha=0.8
        )
        
        # 设置标题和标签
        ax.set_title(title, fontsize=20, fontweight='bold', pad=20)
        ax.set_xlabel('经度', fontsize=14, fontweight='bold')
        ax.set_ylabel('纬度', fontsize=14, fontweight='bold')
        
        # 设置坐标轴
        ax.set_xticks(np.linspace(0, len(pivot_data.columns)-1, 5))
        ax.set_yticks(np.linspace(0, len(pivot_data.index)-1, 5))
        ax.set_xticklabels([f'{x:.3f}' for x in np.linspace(pivot_data.columns.min(), pivot_data.columns.max(), 5)])
        ax.set_yticklabels([f'{y:.3f}' for y in np.linspace(pivot_data.index.min(), pivot_data.index.max(), 5)])
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8, aspect=30)
        cbar.set_label(value_col.replace('_', ' ').title(), fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        # 美化边框
        for spine in ax.spines.values():
            spine.set_linewidth(2)
            spine.set_color('#333333')
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/{filename}', dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.show()
    
    def create_interactive_plotly_heatmap(self, grid_df, value_col, title, filename):
        """创建交互式Plotly热力图"""
        print(f"Creating interactive heatmap for {value_col}...")
        
        # 创建透视表
        pivot_data = grid_df.pivot_table(
            values=value_col,
            index='grid_lat',
            columns='grid_lon',
            aggfunc='mean'
        )
        
        # 创建Plotly热力图
        fig = go.Figure(data=go.Heatmap(
            z=pivot_data.values,
            x=pivot_data.columns,
            y=pivot_data.index,
            colorscale='Viridis',
            hoverongaps=False,
            hovertemplate='经度: %{x}<br>纬度: %{y}<br>值: %{z}<extra></extra>'
        ))
        
        fig.update_layout(
            title={
                'text': title,
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 20, 'family': 'Arial Black'}
            },
            xaxis_title='经度',
            yaxis_title='纬度',
            font=dict(size=12),
            width=1000,
            height=800
        )
        
        fig.write_html(f'{self.results_dir}/{filename}')
        return fig
    
    def create_comprehensive_dashboard(self, grid_df, recommendations):
        """创建综合仪表板"""
        print("Creating comprehensive dashboard...")
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=3,
            subplot_titles=[
                '商业价值热力图', '未来潜力热力图', '综合评分热力图',
                '推荐位置分布', '流量密度分析', '评分对比'
            ],
            specs=[[{"type": "heatmap"}, {"type": "heatmap"}, {"type": "heatmap"}],
                   [{"type": "scatter"}, {"type": "bar"}, {"type": "box"}]]
        )
        
        # 准备数据
        pivot_business = grid_df.pivot_table(values='business_score', index='grid_lat', columns='grid_lon', aggfunc='mean')
        pivot_future = grid_df.pivot_table(values='future_potential_score', index='grid_lat', columns='grid_lon', aggfunc='mean')
        pivot_comprehensive = grid_df.pivot_table(values='comprehensive_score', index='grid_lat', columns='grid_lon', aggfunc='mean')
        
        # 添加热力图
        fig.add_trace(
            go.Heatmap(z=pivot_business.values, x=pivot_business.columns, y=pivot_business.index, 
                      colorscale='Reds', showscale=False),
            row=1, col=1
        )
        
        fig.add_trace(
            go.Heatmap(z=pivot_future.values, x=pivot_future.columns, y=pivot_future.index, 
                      colorscale='Blues', showscale=False),
            row=1, col=2
        )
        
        fig.add_trace(
            go.Heatmap(z=pivot_comprehensive.values, x=pivot_comprehensive.columns, y=pivot_comprehensive.index, 
                      colorscale='Viridis', showscale=False),
            row=1, col=3
        )
        
        # 添加推荐位置散点图
        top_20 = recommendations.head(20)
        fig.add_trace(
            go.Scatter(x=top_20['grid_lon'], y=top_20['grid_lat'], 
                      mode='markers+text',
                      marker=dict(size=top_20['comprehensive_score']/5, 
                                color=top_20['comprehensive_score'],
                                colorscale='Viridis'),
                      text=[f'#{i+1}' for i in range(len(top_20))],
                      textposition="middle center",
                      showlegend=False),
            row=2, col=1
        )
        
        # 添加流量密度分析
        flow_analysis = top_20[['predicted_density', 'start_density', 'end_density']].mean()
        fig.add_trace(
            go.Bar(x=flow_analysis.index, y=flow_analysis.values, 
                  marker_color=['#FF6B6B', '#4ECDC4', '#45B7D1'],
                  showlegend=False),
            row=2, col=2
        )
        
        # 添加评分对比箱线图
        scores = [grid_df['business_score'], grid_df['future_potential_score'], grid_df['comprehensive_score']]
        score_names = ['商业价值', '未来潜力', '综合评分']
        
        for i, (score, name) in enumerate(zip(scores, score_names)):
            fig.add_trace(
                go.Box(y=score, name=name, showlegend=False),
                row=2, col=3
            )
        
        # 更新布局
        fig.update_layout(
            title={
                'text': '摩拜单车商业选址综合分析仪表板',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 24, 'family': 'Arial Black'}
            },
            height=1000,
            showlegend=False
        )
        
        fig.write_html(f'{self.results_dir}/comprehensive_dashboard.html')
        return fig
    
    def create_enhanced_folium_map(self, recommendations, grid_df):
        """创建增强版Folium地图"""
        print("Creating enhanced interactive map...")
        
        # 计算地图中心
        center_lat = recommendations['grid_lat'].mean()
        center_lon = recommendations['grid_lon'].mean()
        
        # 创建地图
        m = folium.Map(
            location=[center_lat, center_lon],
            zoom_start=11,
            tiles=None
        )
        
        # 添加多种地图图层
        folium.TileLayer('OpenStreetMap', name='街道地图').add_to(m)
        folium.TileLayer('Stamen Terrain', name='地形图').add_to(m)
        folium.TileLayer('CartoDB positron', name='简洁地图').add_to(m)
        
        # 创建不同类型的标记群组
        business_cluster = MarkerCluster(name='商业推荐位置').add_to(m)
        potential_cluster = MarkerCluster(name='潜力位置').add_to(m)
        
        # 添加推荐位置标记
        top_10 = recommendations.head(10)
        for idx, row in top_10.iterrows():
            # 根据评分选择颜色
            if row['comprehensive_score'] > 80:
                color = 'red'
                icon = 'star'
            elif row['comprehensive_score'] > 60:
                color = 'orange'
                icon = 'heart'
            else:
                color = 'green'
                icon = 'info-sign'
            
            folium.Marker(
                location=[row['grid_lat'], row['grid_lon']],
                popup=folium.Popup(f"""
                <div style="width:200px">
                <h4>推荐位置 #{idx+1}</h4>
                <hr>
                <b>综合评分:</b> {row['comprehensive_score']:.1f}<br>
                <b>商业价值:</b> {row['business_score']:.1f}<br>
                <b>未来潜力:</b> {row['future_potential_score']:.1f}<br>
                <b>POI总数:</b> {row['total_poi_count']:.0f}<br>
                <b>商业POI:</b> {row['business_poi_count']:.0f}<br>
                <b>预测流量:</b> {row['predicted_density']:.0f}<br>
                <b>坐标:</b> ({row['grid_lat']:.4f}, {row['grid_lon']:.4f})
                </div>
                """, max_width=250),
                tooltip=f"推荐#{idx+1} - 评分:{row['comprehensive_score']:.1f}",
                icon=folium.Icon(color=color, icon=icon)
            ).add_to(business_cluster)
        
        # 添加潜力位置
        potential_locations = recommendations[(recommendations['future_potential_score'] > 70) & 
                                            (recommendations['comprehensive_score'] < 80)].head(10)
        
        for idx, row in potential_locations.iterrows():
            folium.CircleMarker(
                location=[row['grid_lat'], row['grid_lon']],
                radius=8,
                popup=f"""
                <b>潜力位置</b><br>
                未来潜力: {row['future_potential_score']:.1f}<br>
                预测流量: {row['predicted_density']:.0f}
                """,
                color='blue',
                fill=True,
                fillColor='lightblue',
                fillOpacity=0.7
            ).add_to(potential_cluster)
        
        # 添加热力图层
        if 'weighted_flow_density' in grid_df.columns:
            heat_data = []
            sample_grid = grid_df.sample(n=min(5000, len(grid_df)), random_state=42)
            
            for _, row in sample_grid.iterrows():
                if row['weighted_flow_density'] > 0:
                    heat_data.append([
                        row['grid_lat'],
                        row['grid_lon'],
                        row['weighted_flow_density']
                    ])
            
            if heat_data:
                HeatMap(
                    heat_data,
                    name='流量热力图',
                    radius=15,
                    blur=10,
                    max_zoom=1,
                    gradient={0.2: 'blue', 0.4: 'lime', 0.6: 'orange', 1: 'red'}
                ).add_to(m)
        
        # 添加图层控制
        folium.LayerControl().add_to(m)
        
        # 添加全屏插件
        from folium.plugins import Fullscreen
        Fullscreen().add_to(m)
        
        # 保存地图
        m.save(f'{self.results_dir}/enhanced_interactive_map.html')
        print(f"Enhanced interactive map saved to {self.results_dir}/enhanced_interactive_map.html")
        
        return m

def main():
    """主函数"""
    try:
        # 加载数据
        grid_df = pd.read_csv('business_location_analysis.csv')
        recommendations = pd.read_csv('top_business_locations.csv')
        
        # 按综合评分排序
        if 'comprehensive_score' in recommendations.columns:
            recommendations = recommendations.sort_values('comprehensive_score', ascending=False)
        
        # 创建增强版可视化
        viz = EnhancedVisualization()
        
        # 创建美观的热力图
        if 'business_score' in grid_df.columns:
            viz.create_beautiful_heatmap(
                grid_df, 'business_score', 
                '商业价值评分热力图', 
                'business_score_beautiful.png'
            )
        
        if 'future_potential_score' in grid_df.columns:
            viz.create_beautiful_heatmap(
                grid_df, 'future_potential_score', 
                '未来潜力评分热力图', 
                'future_potential_beautiful.png',
                'potential'
            )
        
        if 'comprehensive_score' in grid_df.columns:
            viz.create_beautiful_heatmap(
                grid_df, 'comprehensive_score', 
                '综合评分热力图', 
                'comprehensive_score_beautiful.png',
                'comprehensive'
            )
        
        # 创建交互式热力图
        if 'weighted_flow_density' in grid_df.columns:
            viz.create_interactive_plotly_heatmap(
                grid_df, 'weighted_flow_density',
                '加权流量密度交互式热力图',
                'weighted_flow_interactive.html'
            )
        
        # 创建综合仪表板
        viz.create_comprehensive_dashboard(grid_df, recommendations)
        
        # 创建增强版交互式地图
        viz.create_enhanced_folium_map(recommendations, grid_df)
        
        print("All enhanced visualizations completed!")
        print(f"Results saved in: {viz.results_dir}")
        
    except FileNotFoundError as e:
        print(f"Required data file not found: {e}")
        print("Please run the analysis pipeline first.")
    except Exception as e:
        print(f"Error creating visualizations: {e}")

if __name__ == "__main__":
    main()
