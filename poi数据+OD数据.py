import datetime
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point, Polygon
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import DBSCAN
import contextily as ctx
import folium
from folium.plugins import HeatMap
import os
import re
# 配置参数
GRID_SIZE = 500  # 网格大小(米)
TIME_ZONE = "Asia/Shanghai"  # 时区
POI_CATEGORIES = {
    '公司区': ['金融机构', '公司企业','商务住宅（办公）','写字楼','农林牧渔'],
    '家庭区': ['医疗保健', '生活服务', '科教文化', 
            '汽车相关（维修保养）', '酒店住宿（经济型）',
            '购物消费（超市、便利店）', '商务住宅（住宅）','社区服务'
            ],
    '娱乐区': ['餐饮美食', '休闲娱乐', '酒店住宿（豪华型）', 
            '购物消费（商场，专卖店）', '酒吧','KTV','电影院','农家乐'
             ],
    '交通区':['交通设施','汽车相关','停车场','充电站','加油站'],
    '景区':['旅游景点','公园','纪念馆'],
    '运动健身区': ['运动健身','度假村','体育场馆']
}

# 1. 数据加载与预处理
def load_and_preprocess_data(taxi_file, poi_file):
    # 加载出租车OD数据
    taxi_cols = ['pid', 'dtime', 'dlon', 'dlat']
    taxi = pd.read_csv(taxi_file
                       ,usecols=taxi_cols
                       ,parse_dates=['dtime']
                       ,encoding='utf-8'  # 指定文件编码
                       )
    #usecols是只读取 taxi_cols 列表中指定的列
    #parse_date是将dtime解析为日期时间格式
    
    # 转换时间格式并提取小时
    #确保时间戳列是数值类型
    taxi['dtime']=pd.to_numeric(taxi['dtime'],errors='coerce')
    #生成UTC时间
    taxi['dtime'] = pd.to_datetime(taxi['dtime'], unit='s',utc=True)
    #转化为指定时区的时间
    taxi['dtime'] = taxi['dtime'].dt.tz_convert(TIME_ZONE)
    taxi['hour'] = taxi['dtime'].dt.hour
    taxi['day_of_week'] = taxi['dtime'].dt.dayofweek
    # 加载POI数据
    print(taxi['dtime'])
    
    poi_cols = ['名称', '大类','中类','经度', '纬度']
    poi = pd.read_csv(poi_file, usecols=poi_cols)
    
    # 分类POI
    def classify_poi(row):
        """
        基于类型和名称的详细POI分类
        """
        poi_type=str(row['大类']).strip()
        name=str(row['名称']).lower()
        midclass=str(row['中类']).strip()
        
        # 特殊处理商务住宅类型
        if '商务住宅' in poi_type:
            if any(kw in name for kw in ['写字楼', '大厦', '中心', '广场', '园区', '企业'])\
            or any(kw in midclass for kw in ['产业园','写字楼','办公']):
                return '商务住宅(办公)'
            elif any(kw in name for kw in ['小区', '花园', '家园', '公寓', '别墅', '住宅区'])\
                or any(kw in midclass for kw in ['住宅区','别墅区','社区中心','宿舍','住宅']):
                return '商务住宅(住宅)'
            return '商务住宅(其他)'
        
        # 特殊处理汽车相关
        if '汽车相关' in poi_type:
            if any(kw in name for kw in ['维修', '保养', '美容', '洗车']):
                return '汽车相关(维修保养)'
            elif '4s店' in name:
                return '汽车相关(4S店)'
            elif any(kw in name for kw in ['加油站', '加气站']):
                return '汽车相关(加油站)'
            elif any(kw in name for kw in ['充电站', '充电桩']):
                return '汽车相关(充电站)'
            return '汽车相关(其他)'
        
        # 特殊处理酒店住宿
        if '酒店住宿' in poi_type:
            if any(kw in name for kw in ['豪华', '五星', '四星', '度假', '国际', '庄园']):
                return '酒店住宿(豪华型)'
            elif any(kw in name for kw in ['经济', '快捷', '连锁', '旅馆', '客栈', '民宿']):
                return '酒店住宿(经济型)'
            return '酒店住宿(其他)'
        
        # 特殊处理购物消费
        if '购物消费' in poi_type:
            if any(kw in name for kw in ['商场', '购物中心', '百货', 'mall', '专卖店']):
                return '购物消费(商场/专卖店)'
            elif any(kw in name for kw in ['超市', '便利店', '小卖部']):
                return '购物消费(超市/便利店)'
            elif any(kw in name for kw in ['市场', '农贸市场', '菜市场']):
                return '购物消费(市场)'
            return '购物消费(其他)'
        
        # 特殊处理餐饮美食
        if '餐饮美食' in poi_type:
            if any(kw in name for kw in ['中餐', '餐厅', '饭店', '家常菜']):
                return '餐饮美食(中餐)'
            elif any(kw in name for kw in ['快餐', '汉堡', '披萨', '炸鸡']):
                return '餐饮美食(快餐)'
            elif any(kw in name for kw in ['咖啡', '茶馆', '饮品', '奶茶']):
                return '餐饮美食(饮品)'
            return '餐饮美食(其他)'
        
        # 特殊处理旅游景点
        if '旅游景点' in poi_type:
            if any(kw in name for kw in ['公园', '花园', '绿地']):
                return '旅游景点(公园)'
            elif any(kw in name for kw in ['纪念馆', '博物馆', '故居', '遗迹']):
                return '旅游景点(文化场馆)'
            elif any(kw in name for kw in ['山', '河', '湖', '海', '瀑布', '峡谷']):
                return '旅游景点(自然景观)'
            return '旅游景点(其他)'
        
        # 特殊处理科教文化
        if '科教文化' in poi_type:
            if any(kw in name for kw in ['学校', '小学', '中学', '大学', '学院']):
                return '科教文化(学校)'
            elif any(kw in name for kw in ['幼儿园', '托儿所']):
                return '科教文化(幼儿园)'
            elif any(kw in name for kw in ['图书馆', '文化宫', '博物馆', '科技馆']):
                return '科教文化(文化场馆)'
            return '科教文化(其他)'
        
        # 特殊处理运动健身
        if '运动健身' in poi_type or '度假村' in poi_type:
            if any(kw in name for kw in ['健身', '瑜伽', '健身房', '运动']):
                return '运动健身(健身中心)'
            elif any(kw in name for kw in ['体育', '场馆', '球场', '游泳馆']):
                return '运动健身(体育场馆)'
            elif any(kw in name for kw in ['度假村', '度假中心', '温泉', '山庄']):
                return '运动健身(度假村)'
            return '运动健身(其他)'
        
        # 匹配主要类别
        for category, keywords in POI_CATEGORIES.items():
            if any(kw in poi_type for kw in keywords) or any(kw in midclass for kw in keywords):
                return poi_type
        
        # 检查中类作为备选
        for category, keywords in POI_CATEGORIES.items():
            if any(kw in midclass for kw in keywords):
                return midclass
        
        return '其他'
    #应用分类函数，创建新列，axis=1表示按行
    poi['detailed_type'] = poi.apply(classify_poi, axis=1)
    
    return taxi, poi

# 2. 创建空间网格
def create_spatial_grid(taxi, poi, city_bounds):
    """
        创建空间网格系统并转换坐标系
        taxi:出租车OD数据
        poi:POI数据
        city_bounds:城市边界
    """
    # 添加调试输出，检查城市边界和网格计算
    print(f"创建空间网格... 城市边界: {city_bounds}")
    minx, miny, maxx, maxy = city_bounds
    
    # 检查边界有效性
    if minx >= maxx or miny >= maxy:
        print(f"无效的城市边界: minx={minx}, maxx={maxx}, miny={miny}, maxy={maxy}")
        # 设置默认边界（北京市中心区域）
        minx, miny, maxx, maxy = 116.2, 39.8, 116.5, 40.1
        print(f"使用默认边界: minx={minx}, maxx={maxx}, miny={miny}, maxy={maxy}")
        city_bounds = (minx, miny, maxx, maxy)
    
    # 创建GeoDataFrame
    taxi_gdf = gpd.GeoDataFrame(
        taxi, 
        geometry=gpd.points_from_xy(taxi.dlon, taxi.dlat),
        crs="EPSG:4326"
    )
    
    poi_gdf = gpd.GeoDataFrame(
        poi, 
        geometry=gpd.points_from_xy(poi.经度, poi.纬度),
        crs="EPSG:4326"
    )
    
    # 转换到平面坐标系(单位:米)
    utm_crs = taxi_gdf.estimate_utm_crs()
    taxi_gdf = taxi_gdf.to_crs(utm_crs)
    poi_gdf = poi_gdf.to_crs(utm_crs)
    
    # 重新计算投影后的边界
    minx_proj, miny_proj, maxx_proj, maxy_proj = taxi_gdf.total_bounds
    print(f"投影后边界: minx={minx_proj}, maxx={maxx_proj}, miny={miny_proj}, maxy={maxy_proj}")
    
    # 创建网格
    x_cells = int((maxx_proj - minx_proj) / GRID_SIZE) + 1
    y_cells = int((maxy_proj - miny_proj) / GRID_SIZE) + 1
    print(f"计算网格数: x_cells={x_cells}, y_cells={y_cells}, 总网格数={x_cells * y_cells}")
    
    grid_polygons = []
    for x in range(x_cells):
        for y in range(y_cells):
            x1 = minx_proj + x * GRID_SIZE
            y1 = miny_proj + y * GRID_SIZE
            x2 = x1 + GRID_SIZE
            y2 = y1 + GRID_SIZE
            grid_polygons.append(Polygon([(x1, y1), (x2, y1), (x2, y2), (x1, y2)]))
    
    grid_gdf = gpd.GeoDataFrame(geometry=grid_polygons, crs=utm_crs)
    grid_gdf['grid_id'] = range(len(grid_gdf))
    print(f"创建网格数: {len(grid_gdf)}")
    
    return taxi_gdf, poi_gdf, grid_gdf

# 3. 空间连接与统计
def spatial_join_and_aggregate(taxi_gdf, poi_gdf, grid_gdf):
    """
    执行空间连接并统计网格指标
    """
    # 出租车数据空间连接
    taxi_grid = gpd.sjoin(taxi_gdf, grid_gdf, how='left', predicate='within')
    
    # 按网格和时间段统计
    # 工作日白天 (周一至周五 7:00-19:00)
    workday_day = taxi_grid[
        (taxi_grid['day_of_week'] < 5) & 
        (taxi_grid['hour'] >= 7) & (taxi_grid['hour'] < 19)
    ]
    
    # 工作日夜间 (周一至周五 19:00-次日7:00)
    workday_night = taxi_grid[
        (taxi_grid['day_of_week'] < 5) & 
        ((taxi_grid['hour'] >= 19) | (taxi_grid['hour'] < 7))
    ]
    
    # 周末白天 (周六日 9:00-22:00)
    weekend_day = taxi_grid[
        (taxi_grid['day_of_week'] >= 5) & 
        (taxi_grid['hour'] >= 9) & (taxi_grid['hour'] < 22)
    ]
    
    # 周末夜间 (周六日 22:00-次日9:00)
    weekend_night = taxi_grid[
        (taxi_grid['day_of_week'] >= 5) & 
        ((taxi_grid['hour'] >= 22) | (taxi_grid['hour'] < 9))
    ]
    
    # 统计到达量
    workday_day_counts = workday_day.groupby('grid_id').size().reset_index(name='workday_day_arrival')
    workday_night_counts = workday_night.groupby('grid_id').size().reset_index(name='workday_night_arrival')
    weekend_day_counts = weekend_day.groupby('grid_id').size().reset_index(name='weekend_day_arrival')
    weekend_night_counts = weekend_night.groupby('grid_id').size().reset_index(name='weekend_night_arrival')
    
    # POI数据空间连接
    poi_grid = gpd.sjoin(poi_gdf, grid_gdf, how='left', predicate='within')
    
    # 按类别统计POI数量
    poi_counts = poi_grid.groupby(['grid_id', 'detailed_type']).size().unstack(fill_value=0)
    # 创建详细类型到主类别的映射
    type_mapping = {}
    for main_category, subcategories in POI_CATEGORIES.items():
        for sub in subcategories:
            type_mapping[sub] = main_category
    
    # 将详细类型聚合到主类别
    poi_counts = poi_counts.rename(columns=type_mapping)
    poi_counts = poi_counts.groupby(level=0, axis=1).sum()
    # 确保所有POI分类都存在于数据中，缺失分类填充0
    poi_counts = poi_counts.reindex(columns=POI_CATEGORIES.keys(), fill_value=0)
    
    # 确保所有主类别都存在
    for category in POI_CATEGORIES.keys():
        if category not in poi_counts.columns:
            poi_counts[category] = 0
    
     # 合并所有统计数据
    grid_stats = grid_gdf.merge(workday_day_counts, on='grid_id', how='left')
    grid_stats = grid_stats.merge(workday_night_counts, on='grid_id', how='left')
    grid_stats = grid_stats.merge(weekend_day_counts, on='grid_id', how='left')
    grid_stats = grid_stats.merge(weekend_night_counts, on='grid_id', how='left')
    grid_stats = grid_stats.merge(poi_counts, on='grid_id', how='left')
    
    # 填充缺失值
    grid_stats.fillna(0, inplace=True)
    
     # 计算关键指标
    grid_stats['total_arrival'] = (
        grid_stats['workday_day_arrival'] + 
        grid_stats['workday_night_arrival'] + 
        grid_stats['weekend_day_arrival'] + 
        grid_stats['weekend_night_arrival']
    )
    #计算白天和晚上到达的比率
    grid_stats['workday_day_ratio'] = grid_stats['workday_day_arrival'] / (grid_stats['workday_day_arrival'] + grid_stats['workday_night_arrival'] + 1e-5)
    grid_stats['workday_night_ratio'] = grid_stats['workday_night_arrival'] / (grid_stats['workday_day_arrival'] + grid_stats['workday_night_arrival'] + 1e-5)
    grid_stats['weekend_day_ratio'] = grid_stats['weekend_day_arrival'] / (grid_stats['weekend_day_arrival'] + grid_stats['weekend_night_arrival'] + 1e-5)
    grid_stats['weekend_night_ratio'] = grid_stats['weekend_night_arrival'] / (grid_stats['weekend_day_arrival'] + grid_stats['weekend_night_arrival'] + 1e-5)
    
    # 计算POI类别得分
    for category in ['公司区', '家庭区', '娱乐区','景区','交通区', '运动健身区']:
        category_cols = [col for col in poi_counts.columns if col.startswith(category)]
        #选出category开头的列
        grid_stats[f'{category}_score'] = grid_stats[category_cols].sum(axis=1)
        #计算每个网格的POI得分
    
    return grid_stats

# 4. 区域分类
def classify_areas(grid_stats):
    """
    基于多维度指标的区域分类
    """
     # 计算动态阈值
    thresholds = {
        '公司区': np.percentile(grid_stats['公司区_score'][grid_stats['公司区_score'] > 0], 75) if any(grid_stats['公司区_score'] > 0) else 1,
        '家庭区': np.percentile(grid_stats['家庭区_score'][grid_stats['家庭区_score'] > 0], 75) if any(grid_stats['家庭区_score'] > 0) else 1,
        '娱乐区': np.percentile(grid_stats['娱乐区_score'][grid_stats['娱乐区_score'] > 0], 75) if any(grid_stats['娱乐区_score'] > 0) else 1,
        '运动健身区': np.percentile(grid_stats['运动健身区_score'][grid_stats['运动健身区_score'] > 0], 75) if any(grid_stats['运动健身区_score'] > 0) else 1,
    }
    #每种区域类型都会先筛选出分数大于 0 的记录，对筛选后的正分数计算 75% 分位数
    #如果某个区域类型没有正分数（即所有分数都 <=0），则将阈值设为 1
    # 分类逻辑
    # 分类逻辑
    def classify_row(row):
        """单网格分类逻辑"""
        # 公司区：工作日白天到达主导且公司类POI多
        if (row['workday_day_ratio'] > 0.6 and 
            row['workday_day_arrival'] > 70 and 
            row['公司区_score'] > thresholds['公司区']):
            return "核心公司区"
        
        # 家庭区：工作日夜间到达主导且住宅类POI多
        if (row['workday_night_ratio'] > 0.6 and 
            row['workday_night_arrival'] > 50 and 
            row['家庭区_score'] > thresholds['家庭区']):
            return "成熟家庭区"
        
        # 夜经济区：周末夜间到达主导且娱乐类POI多
        if (row['weekend_night_ratio'] > 0.6 and 
            row['weekend_night_arrival'] > 50 and 
            row['娱乐区_score'] > thresholds['娱乐区']):
            return "活力夜经济区"
        
        # 混合功能区：多指标均较高
        if (row['公司区_score'] > thresholds['公司区'] * 0.7 and
            row['家庭区_score'] > thresholds['家庭区'] * 0.7 and
            row['娱乐区_score'] > thresholds['娱乐区'] * 0.7 and
            row['total_arrival'] > np.percentile(grid_stats['total_arrival'], 75)):
            return "综合功能区"
        
        # 工作日通勤区
        if (row['workday_day_arrival'] > 100 and 
            row['workday_night_arrival'] < 30 and 
            row['公司区_score'] > thresholds['公司区'] * 0.5):
            return "通勤公司区"
        
        # 休闲娱乐区
        if (row['weekend_day_arrival'] > row['workday_day_arrival'] * 1.3 and 
            row['weekend_night_arrival'] > row['workday_night_arrival'] * 1.3 and 
            row['娱乐区_score'] > thresholds['娱乐区'] * 0.6):
            return "休闲娱乐区"
        
        # 运动健身区
        if (row['运动健身区_score'] > thresholds['运动健身区'] and 
            row['weekend_day_arrival'] > 60 and 
            (row['运动健身区_score'] > row['公司区_score'] and row['运动健身区_score'] > row['家庭区_score'])):
            return "运动健身区"
        
        # 住宅主导区
        if (row['家庭区_score'] > thresholds['家庭区'] * 1.2 and 
            row['workday_night_arrival'] > 80 and 
            row['weekend_day_arrival'] > 50):
            return "品质住宅区"
        
        return "其他区域"
    
    grid_stats['area_type'] = grid_stats.apply(classify_row, axis=1)
        
    
    # 商业潜力综合评分
    grid_stats['commercial_score'] = (
        0.20 * grid_stats['total_arrival'] / grid_stats['total_arrival'].max() +
        0.15 * grid_stats['娱乐区_score'] / (grid_stats['娱乐区_score'].max() + 1e-5) +
        0.15 * grid_stats['weekend_night_arrival'] / (grid_stats['weekend_night_arrival'].max() + 1e-5) +
        0.15 * grid_stats['公司区_score'] / (grid_stats['公司区_score'].max() + 1e-5) +
        0.15 * grid_stats['家庭区_score'] / (grid_stats['家庭区_score'].max() + 1e-5) +
        0.10 * grid_stats['运动健身区_score'] / (grid_stats['运动健身区_score'].max() + 1e-5) +
        0.10 * np.log1p(grid_stats.get('购物消费(商场/专卖店)', 0) + grid_stats.get('餐饮美食', 0))
    )
    
    return grid_stats
        
# 5. 商业选址推荐
def recommend_business_locations(grid_stats, poi_gdf):
    """
    基于网格类型生成商业选址推荐
    参数:
    grid_stats: 包含网格类型和商业潜力评分的DataFrame
    poi_gdf: 包含POI信息的GeoDataFrame
    返回:
    包含网格ID、类型、商业潜力评分、推荐业态和选址要求的DataFrame
    """
    # 按区域类型推荐
    recommendations = {
        '核心公司区': {
            '推荐业态': ['商务简餐', '咖啡厅', '便利店', '打印复印', '银行网点', '共享会议室'],
            '选址要点': '写字楼大堂或地铁站出口200米内',
            '营业时间': '工作日7:00-20:00',
            '目标客群': '上班族、商务人士'
        },
        '成熟家庭区': {
            '推荐业态': ['社区超市', '生鲜菜市', '药房', '洗衣店', '幼儿托管', '社区诊所', '宠物服务'],
            '选址要点': '小区主入口100米内，临街店面',
            '营业时间': '7:00-22:00',
            '目标客群': '家庭主妇、老年人、儿童'
        },
        '活力夜经济区': {
            '推荐业态': ['特色餐厅', '酒吧', 'Livehouse', 'KTV', '深夜食堂', '24小时便利店', '网红甜品店'],
            '选址要点': '娱乐场所聚集区的交叉路口',
            '营业时间': '16:00-次日4:00',
            '目标客群': '年轻人、游客'
        },
        '综合功能区': {
            '推荐业态': ['连锁快餐', '品牌咖啡', '健身房', '书店', '美容美发', '数码体验店'],
            '选址要点': '商场一层或地铁连通口',
            '营业时间': '10:00-22:00',
            '目标客群': '全年龄段人群'
        },
        '通勤公司区': {
            '推荐业态': ['早餐车', '简餐便当', '咖啡车', '快递柜', '共享单车点'],
            '选址要点': '地铁站出口或公交站旁',
            '营业时间': '工作日7:00-10:00, 17:00-20:00',
            '目标客群': '通勤上班族'
        },
        '休闲娱乐区': {
            '推荐业态': ['主题餐厅', '手作工坊', '电竞馆', '剧本杀', '户外用品', '摄影工作室'],
            '选址要点': '公园或景点入口附近',
            '营业时间': '周末10:00-24:00',
            '目标客群': '年轻人、家庭'
        },
        '运动健身区': {
            '推荐业态': ['健身中心', '运动装备店', '健康餐厅', '体育场馆', '瑜伽馆', '户外俱乐部'],
            '选址要点': '交通便利区域或大型社区附近',
            '营业时间': '6:00-22:00',
            '目标客群': '健身爱好者、运动人群、周边居民'
        },
        '品质住宅区': {
            '推荐业态': ['精品超市', '进口食品', '瑜伽馆', '早教中心', '高端宠物店', '茶艺馆'],
            '选址要点': '社区商业街中心位置',
            '营业时间': '10:00-22:00',
            '目标客群': '中高收入家庭'
        }
    }
    
    # 动态调整高潜力阈值
    score_threshold = 0.7
    high_potential = grid_stats[grid_stats['commercial_score'] > score_threshold].copy()
    
    # 如果高潜力网格太少，降低阈值
    if len(high_potential) < 10:
        score_threshold = max(0.3, score_threshold - 0.2)
        high_potential = grid_stats[grid_stats['commercial_score'] > score_threshold].copy()
        print(f"调整阈值至{score_threshold}，高潜力网格数: {len(high_potential)}")
    
    print(f"高潜力网格数: {len(high_potential)}")
    
    # 如果仍然没有高潜力网格，使用Top N网格
    if len(high_potential) == 0:
        top_n = min(20, len(grid_stats))
        high_potential = grid_stats.nlargest(top_n, 'commercial_score').copy()
        print(f"未找到符合阈值的网格，使用Top {top_n}网格")
    
    # 添加推荐信息
    high_potential['recommendation'] = high_potential['area_type'].map(
        lambda x: recommendations.get(x, {
            '推荐业态': [],
            '选址要点': '待分析',
            '营业时间': '待分析',
            '目标客群': '待分析'
        })
    )
    
    # 提取推荐详情
    high_potential['business_types'] = high_potential['recommendation'].apply(
        lambda x: x['推荐业态'])
    high_potential['location_requirements'] = high_potential['recommendation'].apply(
        lambda x: x['选址要点'])
    high_potential['operation_hours'] = high_potential['recommendation'].apply(
        lambda x: x['营业时间'])
    high_potential['target_customers'] = high_potential['recommendation'].apply(
        lambda x: x['目标客群'])
    
    #计算竞争力指数
    def calculate_competition(row):
        """
        计算商业竞争力指数
        """
        area_type=row['area_type']
        if area_type not in recommendations:
            print(f"该区域类型'{area_type}'的推荐信息未找到")
            return 1.0
        
        #获取推荐业态关键词
        business_keywords = recommendations[area_type]['推荐业态']
        
        #计算POI中包含推荐业态关键词的数量(同类型POI数量）
        #该指标反映了区域内推荐业态的聚集程度，数量越多竞争越激烈
        same_type_count=0
        for keyword in business_keywords:
            same_type_count += sum(col for col_name, col in row.items() 
                                  if re.search(keyword, col_name) and isinstance(col, (int, float)))
        
        # 计算POI多样性 (香农熵)
        #信息熵能够同时考虑类别数量和分布均匀性
        #当所有类别均匀分布时，熵值最大，表明多样性最好
        #当某一类别占主导时，熵值降低，反映多样性不足
        poi_counts = [col for col_name, col in row.items() 
                     if col_name in POI_CATEGORIES.keys() or '(' in col_name]
        total_poi = sum(poi_counts)
        if total_poi == 0:
            return 1.0
        #计算香农熵，使用信息熵公式计算区域内 POI 类别的多样性 
        #公式：H = -Σ(p_i * log (p_i))，其中 p_i 是第 i 类 POI 的比例
        #多样性越高，说明区域功能越丰富，竞争力可能越强
        entropy = 0
        for count in poi_counts:
            p = count / total_poi
            if p > 0:
                entropy -= p * np.log(p)     
        
        # 竞争指数 = 多样性 / (同类型POI数量+1)
        #+1避免分母为0
        return entropy / (same_type_count + 1)
        #可以体现多样性高的，竞争小的（同类型POI数量少），具有更高的竞争力
    
    #计算竞争力指数   
    high_potential['competition_index'] = high_potential.apply(calculate_competition, axis=1)
    
    return high_potential[['grid_id', 'geometry', 'area_type', 'commercial_score', 
                          'competition_index', 'business_types', 'location_requirements',
                          'operation_hours', 'target_customers']]
 
# 6. 商圈聚类分析
def identify_commercial_clusters(high_potential, poi_gdf):
    # 检查高潜力网格是否为空
    if high_potential.empty:
        print("没有高潜力网格，无法进行商圈聚类")
        return pd.DataFrame(), pd.DataFrame()
    # 获取网格中心点
    high_potential['center'] = high_potential['geometry'].centroid
    points = np.array([[point.x, point.y] for point in high_potential['center']])
    
    # DBSCAN聚类
    db = DBSCAN(eps=GRID_SIZE*3, min_samples=3).fit(points)
    high_potential['cluster'] = db.labels_
    
    # 筛选有效聚类（排除噪声点）
    clusters = high_potential[high_potential['cluster'] != -1]
    
    #如果没有有效聚类，返回空数据框
    if clusters.empty:
        print("没有有效聚类，返回空数据框")
        return pd.DataFrame(), pd.DataFrame()
    
    # 计算聚类特征
    cluster_groups = clusters.dissolve(
        by='cluster', 
        aggfunc={
            'commercial_score': 'mean',
            'competition_index': 'mean',
            'geometry': 'centroid'
        }
    ).reset_index()
    
    cluster_groups.rename(columns={
        'geometry': 'cluster_center',
        'commercial_score': 'avg_commercial_score',
        'competition_index': 'avg_competition_index'
    }, inplace=True)
    
    #添加商圈类型标签
    def label_cluster(group):
        """根据聚类内主要区域类型标注商圈"""
        area_counts = group['area_type'].value_counts()
        primary_area = area_counts.idxmax() if not area_counts.empty else "综合商圈"
        
        if '核心公司区' in primary_area:
            return "CBD商圈"
        elif '活力夜经济区' in primary_area:
            return "夜生活商圈"
        elif '成熟家庭区' in primary_area or '品质住宅区' in primary_area:
            return "社区商圈"
        elif '休闲娱乐区' in primary_area:
            return "休闲商圈"
        else:
            return "综合商圈"
    
    # 获取每个聚类的区域类型分布
    cluster_area_dist = clusters.groupby('cluster')['area_type'].apply(
        lambda x: x.value_counts().to_dict()).reset_index(name='area_dist')
    
    cluster_groups = cluster_groups.merge(cluster_area_dist, on='cluster')
    cluster_groups['cluster_type'] = cluster_groups['area_dist'].apply(
        lambda x: label_cluster(pd.Series(x)))
    
    return clusters, cluster_groups


# 7. 可视化分析
# 7. 可视化分析
def visualize_results(grid_stats, clusters, cluster_centers, output_dir="results"):
    """生成可视化分析结果"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 转换回地理坐标系
    grid_stats = grid_stats.to_crs("EPSG:4326")
    
    if not clusters.empty:
        clusters = clusters.to_crs("EPSG:4326")
    if not cluster_centers.empty:
        cluster_centers = cluster_centers.to_crs("EPSG:4326")
    
    # 1. 区域类型分布图
    fig, ax = plt.subplots(figsize=(14, 12))
    
    # 定义颜色映射
    area_colors = {
        '核心公司区': 'blue',
        '成熟家庭区': 'green',
        '活力夜经济区': 'purple',
        '综合功能区': 'orange',
        '通勤公司区': 'lightblue',
        '休闲娱乐区': 'pink',
        '品质住宅区': 'lightgreen',
        '其他区域': 'gray'
    }
    
    # 绘制区域类型
    for area_type, color in area_colors.items():
        subset = grid_stats[grid_stats['area_type'] == area_type]
        if not subset.empty:
            subset.plot(ax=ax, color=color, label=area_type, alpha=0.7)
    
    # 添加聚类中心
    if not cluster_centers.empty:
        cluster_centers.plot(ax=ax, marker='*', color='red', markersize=50, label='商圈中心')
        
        # 标注商圈类型
        for idx, row in cluster_centers.iterrows():
            ax.annotate(
                row['cluster_type'], 
                xy=(row['cluster_center'].x, row['cluster_center'].y),
                xytext=(3, 3), textcoords="offset points",
                fontsize=9, bbox=dict(boxstyle="round,pad=0.3", fc="white", alpha=0.7)
            )
    
    ctx.add_basemap(ax, crs=grid_stats.crs.to_string(), source=ctx.providers.CartoDB.Positron)
    ax.legend(title='区域类型', loc='upper left')
    plt.title('城市功能区与商圈分布')
    plt.savefig(f"{output_dir}/area_classification.png", dpi=300, bbox_inches='tight')
    
    # 2. 交互式热力图
    m = folium.Map(location=[grid_stats.geometry.centroid.y.mean(), 
                            grid_stats.geometry.centroid.x.mean()], 
                  zoom_start=12, 
                  tiles='CartoDB positron',
                  control_scale=True)
    
    # 添加到达热力图
    arrival_data = [[point.y, point.x, count] for point, count in 
                   zip(grid_stats.geometry.centroid, grid_stats['total_arrival'])]
    HeatMap(arrival_data, name='出租车到达量', radius=8, max_zoom=15).add_to(m)
    
    # 添加POI聚类
    if not clusters.empty:
        for _, row in clusters.iterrows():
            folium.CircleMarker(
                location=[row['center'].y, row['center'].x],
                radius=row['commercial_score'] * 8 + 3,
                color=area_colors.get(row['area_type'], 'gray'),
                fill=True,
                fill_opacity=0.7,
                popup=folium.Popup(
                    f"<b>{row['area_type']}</b><br>"
                    f"潜力分: {row['commercial_score']:.2f}<br>"
                    f"竞争指数: {row['competition_index']:.2f}<br>"
                    f"推荐业态: {', '.join(row['business_types'][:3])}",
                    max_width=250
                )
            ).add_to(m)
    
    # 添加聚类中心
    if not cluster_centers.empty:
        for _, row in cluster_centers.iterrows():
            folium.Marker(
                location=[row['cluster_center'].y, row['cluster_center'].x],
                icon=folium.Icon(color='red', icon='star', prefix='fa'),
                popup=folium.Popup(
                    f"<b>{row['cluster_type']}</b><br>"
                    f"平均潜力分: {row['avg_commercial_score']:.2f}<br>"
                    f"平均竞争指数: {row['avg_competition_index']:.2f}<br>"
                    f"主要区域: {', '.join(list(row['area_dist'].keys())[:3])}",
                    max_width=250
                )
            ).add_to(m)
    
    folium.LayerControl().add_to(m)
    m.save(f"{output_dir}/commercial_potential_map.html")
    
    # 3. 统计图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 14))
    
    # 区域类型分布
    type_counts = grid_stats['area_type'].value_counts()
    axes[0, 0].bar(type_counts.index, type_counts, color=[area_colors.get(t, 'gray') for t in type_counts.index])
    axes[0, 0].set_title('区域类型分布', fontsize=12)
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # 到达量时间分布
    time_data = {
        '工作日白天': grid_stats['workday_day_arrival'].sum(),
        '工作日夜间': grid_stats['workday_night_arrival'].sum(),
        '周末白天': grid_stats['weekend_day_arrival'].sum(),
        '周末夜间': grid_stats['weekend_night_arrival'].sum()
    }
    axes[0, 1].bar(time_data.keys(), time_data.values(), color=['skyblue', 'navy', 'lightgreen', 'darkgreen'])
    axes[0, 1].set_title('出租车到达量时间分布', fontsize=12)
    
    # POI类型分布
    poi_cols = [col for col in grid_stats.columns if col in POI_CATEGORIES.keys() or '(' in col]
    poi_sums = grid_stats[poi_cols].sum().sort_values(ascending=False).head(10)
    axes[1, 0].barh(poi_sums.index, poi_sums, color='purple')
    axes[1, 0].set_title('TOP 10 POI类型分布', fontsize=12)
    
    # 商业潜力分布
    commercial_bins = pd.cut(grid_stats['commercial_score'], bins=[0, 0.3, 0.5, 0.7, 0.9, 1.0])
    bin_counts = commercial_bins.value_counts().sort_index()
    axes[1, 1].bar(bin_counts.index.astype(str), bin_counts, color='gold')
    axes[1, 1].set_title('商业潜力分布', fontsize=12)
    axes[1, 1].set_xlabel('商业潜力评分区间')
    
    plt.tight_layout(pad=3.0)
    plt.savefig(f"{output_dir}/analysis_charts.png", dpi=300)
    
    return m

# 主函数
def main():
    # 输入文件路径
    taxi_file = "D:\桌面\比赛文件\商业选址\OD_ALL.csv"
    poi_file = "D:\桌面\比赛文件\商业选址\北京市POI数据.csv"
    
    # 城市边界（示例值，需要根据实际城市调整）
    # 格式: (min_lng, min_lat, max_lng, max_lat)
    city_bounds = (115.25, 39.28, 117.30, 41.05)  # 北京大致边界
    
    now_time=datetime.datetime.now().strftime('%H:%M:%S')
    print(f"{now_time} 开始商业选址分析...")
    
    # 1. 数据加载与预处理
    print(f"{now_time} 加载和预处理数据...")
    taxi, poi = load_and_preprocess_data(taxi_file, poi_file)
    print(f"出租车记录数: {len(taxi)}, POI记录数: {len(poi)}")
    
    # 2. 创建空间网格
    print(f"{now_time} 创建空间网格...")
    taxi_gdf, poi_gdf, grid_gdf = create_spatial_grid(taxi, poi, city_bounds)
    print(f"创建网格数: {len(grid_gdf)}")
    
    # 3. 空间统计
    print(f"{now_time} 进行空间统计分析...")
    grid_stats = spatial_join_and_aggregate(taxi_gdf, poi_gdf, grid_gdf)
    
    # 4. 区域分类
    print(f"{now_time} 分类区域类型...")
    grid_stats = classify_areas(grid_stats)
    
    # 5. 商业选址推荐
    print(f"{now_time} 生成商业选址推荐...")
    high_potential = recommend_business_locations(grid_stats, poi_gdf)
    print(f"高潜力网格数: {len(high_potential)}")
    
    # 6. 商圈聚类
    print(f"{now_time} 识别商圈聚类...")
    clusters, cluster_centers = identify_commercial_clusters(high_potential, poi_gdf)
    if not clusters.empty:
        print(f"识别商圈数: {cluster_centers['cluster'].nunique()}")
    
    # 7. 可视化
    print(f"{now_time} 生成可视化结果...")
    map_obj = visualize_results(grid_stats, clusters, cluster_centers)
    
    print(f"{now_time} 分析完成！结果保存在 results/ 目录")
    
    # 输出Top推荐位置
    if not high_potential.empty:
        print("\nTop 5 推荐选址位置：")
        top_locations = high_potential.sort_values(
            ['commercial_score', 'competition_index'], 
            ascending=[False, False]
        ).head(5)
        
        for i, (_, row) in enumerate(top_locations.iterrows(), 1):
            center = row['geometry'].centroid
            print(f"{i}. 网格ID: {row['grid_id']} | 类型: {row['area_type']} | 潜力分: {row['commercial_score']:.2f} | 竞争指数: {row['competition_index']:.2f}")
            print(f"   推荐业态: {', '.join(row['business_types'][:3])}")
            print(f"   选址要求: {row['location_requirements']}")
            print(f"   营业时间: {row['operation_hours']}")
            print(f"   目标客群: {row['target_customers']}")
            print(f"   中心坐标: ({center.x:.6f}, {center.y:.6f})")
            print("-"*80)

if __name__ == "__main__":
    main()