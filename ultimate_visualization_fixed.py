"""
摩拜单车商业选址推荐系统 - 终极可视化（中文字体修复版）
整合所有重要的可视化结果，提供最佳的展示效果
"""

import pandas as pd
import numpy as np
import geopandas as gpd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
import folium
from folium.plugins import HeatMap, MarkerCluster, Fullscreen
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 中文字体设置函数
def setup_chinese_font():
    """设置中文字体以确保中文正常显示"""
    # 获取系统字体列表
    font_list = [f.name for f in fm.fontManager.ttflist]
    
    # 扩展中文字体选项，增加更多常用中文字体
    chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong', 
                     'Arial Unicode MS', 'Heiti TC', 'WenQuanYi Micro Hei', 'Heiti TC',
                     'NSimSun', 'SimKai', 'Microsoft JhengHei', 'PingFang SC', 'Hiragino Sans GB']
    
    # 尝试设置中文字体
    for font in chinese_fonts:
        if font in font_list:
            # 设置全局字体
            plt.rcParams['font.family'] = ['sans-serif']
            plt.rcParams['font.sans-serif'] = [font, 'WenQuanYi Micro Hei', 'Heiti TC', 'SimHei']
            plt.rcParams['axes.unicode_minus'] = False
            plt.rcParams['font.size'] = 12
            plt.rcParams['axes.titlesize'] = 14
            plt.rcParams['axes.labelsize'] = 12
            plt.rcParams['xtick.labelsize'] = 10
            plt.rcParams['ytick.labelsize'] = 10
            plt.rcParams['legend.fontsize'] = 11
            plt.rcParams['figure.titlesize'] = 18
            print(f"Using Chinese font: {font}")
            
            # 创建字体测试图表
            create_font_test_chart(font)
            return font
    
    # 如果没有找到中文字体，使用默认字体并强制设置
    print("Warning: No Chinese font found, using default font with fallback")
    plt.rcParams['font.family'] = ['sans-serif']
    plt.rcParams['font.sans-serif'] = ['SimHei', 'WenQuanYi Micro Hei', 'Heiti TC', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 12
    create_font_test_chart('SimHei')
    return 'SimHei'

def create_font_test_chart(font_name):
    """创建一个简单的图表来测试中文字体显示"""
    print(f"Creating font test chart with font: {font_name}")
    fig, ax = plt.subplots(figsize=(8, 5))
    ax.text(0.5, 0.7, f"测试中文字体: {font_name}", ha='center', va='center', fontsize=18, fontweight='bold')
    ax.text(0.5, 0.5, "这是一个中文测试文本，用于验证字体是否正常显示。", ha='center', va='center', fontsize=14)
    ax.text(0.5, 0.3, "标题、坐标轴标签和图例都应该正确显示中文。", ha='center', va='center', fontsize=12)
    ax.axis('off')
    plt.tight_layout()
    plt.savefig(f'results/ultimate_viz_fixed/font_test.png', dpi=300, bbox_inches='tight')
    plt.close()

# 设置字体
setup_chinese_font()
plt.style.use('default')  # 使用默认样式避免兼容性问题

class UltimateVisualizationFixed:
    """终极可视化类（中文字体修复版）"""
    
    def __init__(self):
        self.results_dir = 'results/ultimate_viz_fixed'
        self.grid_gdf = None
        self.poi_data = None
        self.ensure_results_dir()
        
        # 专业配色方案
        self.colors = {
            'business': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'],
            'heatmap_business': ['#ffffcc', '#ffeda0', '#fed976', '#feb24c', '#fd8d3c', '#fc4e2a', '#e31a1c', '#bd0026', '#800026'],
            'heatmap_flow': ['#f7fbff', '#deebf7', '#c6dbef', '#9ecae1', '#6baed6', '#4292c6', '#2171b5', '#08519c', '#08306b'],
            'heatmap_potential': ['#f7fcf5', '#e5f5e0', '#c7e9c0', '#a1d99b', '#74c476', '#41ab5d', '#238b45', '#006d2c', '#00441b'],
            'comprehensive': ['#440154', '#31688e', '#35b779', '#fde725']
        }
    
    def ensure_results_dir(self):
        """确保结果目录存在"""
        import os
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
    
    def load_all_data(self):
        """加载所有必要数据"""
        print("Loading all data for ultimate visualization...")
        
        try:
            # 加载网格分析结果
            grid_shp_path = 'results/grid_analysis/grid_analysis_results.shp'
            self.grid_gdf = gpd.read_file(grid_shp_path)
            
            # 修复列名
            column_mapping = {
                'total_poi_': 'total_poi_count',
                'business_p': 'business_poi_count',
                'transport_': 'transport_poi_count',
                'education_': 'education_poi_count',
                'medical_po': 'medical_poi_count',
                'bike_flow_': 'bike_flow_density',
                'weighted_f': 'weighted_flow_density',
                'start_dens': 'start_density',
                'end_densit': 'end_density',
                'predicted_': 'predicted_density',
                'test_start': 'test_start_density',
                'business_s': 'business_score',
                'future_pot': 'future_potential_score',
                'comprehens': 'comprehensive_score'
            }
            self.grid_gdf = self.grid_gdf.rename(columns=column_mapping)
            
            print(f"Grid data loaded: {len(self.grid_gdf)} cells")
            
            # 加载POI数据
            self.poi_data = pd.read_csv('北京市POI数据.csv')
            print(f"POI data loaded: {len(self.poi_data)} records")
            
            return True
            
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
    
    def create_master_heatmap_dashboard(self):
        """创建主热力图仪表板（中文字体修复版）"""
        print("Creating master heatmap dashboard with Chinese font support...")
        
        fig, axes = plt.subplots(2, 2, figsize=(24, 20))
        
        # 设置总标题
        fig.suptitle('摩拜单车商业选址热力图仪表板', fontsize=28, fontweight='bold', y=0.95)
        
        # 1. 综合评分热力图
        self.grid_gdf.plot(
            column='comprehensive_score',
            cmap=LinearSegmentedColormap.from_list("comprehensive", self.colors['comprehensive']),
            linewidth=0.02,
            ax=axes[0, 0],
            edgecolor='white',
            alpha=0.8,
            legend=True,
            legend_kwds={'shrink': 0.8, 'label': '综合评分'}
        )
        axes[0, 0].set_title('综合评分热力图', fontsize=18, fontweight='bold', pad=15)
        axes[0, 0].set_xlabel('经度', fontsize=14)
        axes[0, 0].set_ylabel('纬度', fontsize=14)
        axes[0, 0].tick_params(labelsize=10)
        
        # 2. 商业价值热力图
        self.grid_gdf.plot(
            column='business_score',
            cmap=LinearSegmentedColormap.from_list("business", self.colors['heatmap_business']),
            linewidth=0.02,
            ax=axes[0, 1],
            edgecolor='white',
            alpha=0.8,
            legend=True,
            legend_kwds={'shrink': 0.8, 'label': '商业价值评分'}
        )
        axes[0, 1].set_title('商业价值热力图', fontsize=18, fontweight='bold', pad=15)
        axes[0, 1].set_xlabel('经度', fontsize=14)
        axes[0, 1].set_ylabel('纬度', fontsize=14)
        axes[0, 1].tick_params(labelsize=10)
        
        # 3. 未来潜力热力图
        self.grid_gdf.plot(
            column='future_potential_score',
            cmap=LinearSegmentedColormap.from_list("potential", self.colors['heatmap_potential']),
            linewidth=0.02,
            ax=axes[1, 0],
            edgecolor='white',
            alpha=0.8,
            legend=True,
            legend_kwds={'shrink': 0.8, 'label': '未来潜力评分'}
        )
        axes[1, 0].set_title('未来潜力热力图', fontsize=18, fontweight='bold', pad=15)
        axes[1, 0].set_xlabel('经度', fontsize=14)
        axes[1, 0].set_ylabel('纬度', fontsize=14)
        axes[1, 0].tick_params(labelsize=10)
        
        # 4. 加权流量密度热力图
        self.grid_gdf.plot(
            column='weighted_flow_density',
            cmap=LinearSegmentedColormap.from_list("flow", self.colors['heatmap_flow']),
            linewidth=0.02,
            ax=axes[1, 1],
            edgecolor='white',
            alpha=0.8,
            legend=True,
            legend_kwds={'shrink': 0.8, 'label': '加权流量密度'}
        )
        axes[1, 1].set_title('加权流量密度热力图', fontsize=18, fontweight='bold', pad=15)
        axes[1, 1].set_xlabel('经度', fontsize=14)
        axes[1, 1].set_ylabel('纬度', fontsize=14)
        axes[1, 1].tick_params(labelsize=10)
        
        # 美化所有子图
        for ax in axes.flat:
            ax.set_facecolor('#f8f9fa')
            ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)
            for spine in ax.spines.values():
                spine.set_linewidth(1.5)
                spine.set_color('#333333')
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/master_heatmap_dashboard_fixed.png', 
                   dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
    
    def create_business_analysis_dashboard(self):
        """创建商业分析仪表板（中文字体修复版）"""
        print("Creating business analysis dashboard with Chinese font support...")
        
        # 获取推荐数据
        top_20 = self.grid_gdf.nlargest(20, 'comprehensive_score')
        
        fig, axes = plt.subplots(2, 3, figsize=(24, 16))
        fig.suptitle('商业选址分析仪表板', fontsize=24, fontweight='bold', y=0.95)
        
        # 1. 推荐位置评分对比
        bars = axes[0, 0].bar(range(len(top_20.head(10))), top_20.head(10)['comprehensive_score'], 
                             color=self.colors['business'], alpha=0.8, edgecolor='black', linewidth=1)
        axes[0, 0].set_title('前10推荐位置综合评分', fontsize=16, fontweight='bold')
        axes[0, 0].set_xlabel('推荐排名', fontsize=12)
        axes[0, 0].set_ylabel('综合评分', fontsize=12)
        axes[0, 0].set_xticks(range(10))
        axes[0, 0].set_xticklabels([f'第{i+1}名' for i in range(10)], rotation=45)
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + 1,
                           f'{height:.1f}', ha='center', va='bottom', fontweight='bold')
        
        # 2. POI分布统计
        poi_counts = self.poi_data['大类'].value_counts().head(8)
        axes[0, 1].pie(poi_counts.values, labels=poi_counts.index, 
                      autopct='%1.1f%%', startangle=90,
                      colors=plt.cm.Set3(np.linspace(0, 1, len(poi_counts))))
        axes[0, 1].set_title('北京市POI分布', fontsize=16, fontweight='bold')
        
        # 3. 评分相关性分析
        score_data = top_20[['business_score', 'future_potential_score', 'comprehensive_score']]
        correlation = score_data.corr()
        im = axes[0, 2].imshow(correlation, cmap='RdYlBu_r', aspect='auto', vmin=-1, vmax=1)
        axes[0, 2].set_xticks(range(len(correlation.columns)))
        axes[0, 2].set_yticks(range(len(correlation.columns)))
        axes[0, 2].set_xticklabels(['商业价值', '未来潜力', '综合评分'], rotation=45)
        axes[0, 2].set_yticklabels(['商业价值', '未来潜力', '综合评分'])
        axes[0, 2].set_title('评分相关性矩阵', fontsize=16, fontweight='bold')
        
        # 添加相关性数值
        for i in range(len(correlation.columns)):
            for j in range(len(correlation.columns)):
                axes[0, 2].text(j, i, f'{correlation.iloc[i, j]:.2f}',
                               ha="center", va="center", color="black", fontweight='bold')
        
        # 4. 推荐位置地理分布
        scatter = axes[1, 0].scatter(top_20['center_lon'], top_20['center_lat'], 
                                    c=top_20['comprehensive_score'], s=top_20['comprehensive_score']*3,
                                    cmap='viridis', alpha=0.7, edgecolors='black', linewidth=1)
        axes[1, 0].set_title('推荐位置地理分布', fontsize=16, fontweight='bold')
        axes[1, 0].set_xlabel('经度', fontsize=12)
        axes[1, 0].set_ylabel('纬度', fontsize=12)
        
        # 标注前5名
        for i, (idx, row) in enumerate(top_20.head(5).iterrows()):
            axes[1, 0].annotate(f'#{i+1}', (row['center_lon'], row['center_lat']),
                               xytext=(5, 5), textcoords='offset points', fontweight='bold',
                               bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
        
        plt.colorbar(scatter, ax=axes[1, 0], label='综合评分')
        
        # 5. 流量类型分析
        flow_types = ['start_density', 'end_density', 'predicted_density', 'test_start_density']
        flow_labels = ['起点密度', '终点密度', '预测密度', '测试起点密度']
        flow_means = [top_20[col].mean() for col in flow_types]
        
        bars = axes[1, 1].bar(flow_labels, flow_means, 
                             color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
                             alpha=0.8, edgecolor='black', linewidth=1)
        axes[1, 1].set_title('平均流量密度分析', fontsize=16, fontweight='bold')
        axes[1, 1].set_ylabel('平均密度', fontsize=12)
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                           f'{height:.0f}', ha='center', va='bottom', fontweight='bold')
        
        # 6. 评分分布箱线图
        box_plot = axes[1, 2].boxplot([score_data['business_score'], score_data['future_potential_score'], 
                                      score_data['comprehensive_score']], 
                                     labels=['商业价值', '未来潜力', '综合评分'],
                                     patch_artist=True)
        
        colors = ['lightblue', 'lightgreen', 'lightcoral']
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        axes[1, 2].set_title('评分分布箱线图', fontsize=16, fontweight='bold')
        axes[1, 2].set_ylabel('评分值', fontsize=12)
        axes[1, 2].tick_params(axis='x', rotation=45)
        
        # 美化所有子图
        for ax in axes.flat:
            ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)
            for spine in ax.spines.values():
                spine.set_linewidth(1.5)
                spine.set_color('#333333')
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/business_analysis_dashboard_fixed.png', 
                   dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()
    
    def create_simple_summary_chart(self):
        """创建简化的总结图表"""
        print("Creating simple summary chart...")
        
        top_10 = self.grid_gdf.nlargest(10, 'comprehensive_score')
        
        fig, axes = plt.subplots(1, 2, figsize=(20, 8))
        fig.suptitle('摩拜单车商业选址推荐总结', fontsize=20, fontweight='bold')
        
        # 1. 推荐位置评分
        bars = axes[0].bar(range(len(top_10)), top_10['comprehensive_score'], 
                          color='skyblue', alpha=0.8, edgecolor='navy', linewidth=2)
        axes[0].set_title('前10推荐位置综合评分', fontsize=16, fontweight='bold')
        axes[0].set_xlabel('推荐排名', fontsize=14)
        axes[0].set_ylabel('综合评分', fontsize=14)
        axes[0].set_xticks(range(10))
        axes[0].set_xticklabels([f'第{i+1}名' for i in range(10)])
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            axes[0].text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'{height:.1f}', ha='center', va='bottom', fontweight='bold', fontsize=12)
        
        # 2. 关键指标对比
        metrics = ['商业价值', '未来潜力', 'POI密度', '流量密度']
        values = [
            top_10['business_score'].mean(),
            top_10['future_potential_score'].mean(),
            top_10['total_poi_count'].mean() / 20,  # 缩放到合适范围
            top_10['weighted_flow_density'].mean() / 50  # 缩放到合适范围
        ]
        
        bars = axes[1].bar(metrics, values, 
                          color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
                          alpha=0.8, edgecolor='black', linewidth=2)
        axes[1].set_title('推荐位置平均指标', fontsize=16, fontweight='bold')
        axes[1].set_ylabel('标准化数值', fontsize=14)
        axes[1].tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            axes[1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                        f'{height:.1f}', ha='center', va='bottom', fontweight='bold', fontsize=12)
        
        # 美化图表
        for ax in axes:
            ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)
            for spine in ax.spines.values():
                spine.set_linewidth(2)
                spine.set_color('#333333')
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/simple_summary_chart.png', 
                   dpi=300, bbox_inches='tight', facecolor='white')
        plt.show()

def main():
    """主函数"""
    print("🚀 Starting Ultimate Visualization System (Chinese Font Fixed)...")
    
    # 创建终极可视化对象
    viz = UltimateVisualizationFixed()
    
    # 加载所有数据
    if not viz.load_all_data():
        print("❌ Failed to load data")
        return
    
    print("✅ Data loaded successfully!")
    
    # 创建可视化
    print("\n📊 Creating visualizations with Chinese font support...")
    
    # 1. 主热力图仪表板
    viz.create_master_heatmap_dashboard()
    
    # 2. 商业分析仪表板
    viz.create_business_analysis_dashboard()
    
    # 3. 简化总结图表
    viz.create_simple_summary_chart()
    
    print(f"\n🎉 All visualizations completed!")
    print(f"📁 Results saved in: {viz.results_dir}")

if __name__ == "__main__":
    main()
