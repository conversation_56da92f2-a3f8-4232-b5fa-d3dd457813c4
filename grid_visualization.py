import pandas as pd
import numpy as np
import geopandas as gpd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
import folium
from folium.plugins import HeatMap
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class GridVisualization:
    """基于网格的可视化类"""
    
    def __init__(self, grid_results_path):
        self.grid_results_path = grid_results_path
        self.grid_gdf = None
        self.results_dir = 'results/grid_visualization'
        self.ensure_results_dir()
        
        # 定义美观的颜色方案
        self.color_schemes = {
            'business': ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D'],
            'flow': ['#0B132B', '#1C2541', '#3A506B', '#5BC0BE', '#6FFFE9'],
            'potential': ['#264653', '#2A9D8F', '#E9C46A', '#F4A261', '#E76F51'],
            'comprehensive': ['#03045E', '#023E8A', '#0077B6', '#0096C7', '#00B4D8', '#48CAE4']
        }
    
    def ensure_results_dir(self):
        """确保结果目录存在"""
        import os
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
    
    def load_grid_results(self):
        """加载网格分析结果"""
        print("Loading grid analysis results...")
        try:
            self.grid_gdf = gpd.read_file(self.grid_results_path)
            print(f"Grid results loaded: {len(self.grid_gdf)} cells")

            # 修复列名（shp文件会截断长列名）
            column_mapping = {
                'total_poi_': 'total_poi_count',
                'business_p': 'business_poi_count',
                'transport_': 'transport_poi_count',
                'education_': 'education_poi_count',
                'medical_po': 'medical_poi_count',
                'bike_flow_': 'bike_flow_density',
                'weighted_f': 'weighted_flow_density',
                'start_dens': 'start_density',
                'end_densit': 'end_density',
                'predicted_': 'predicted_density',
                'test_start': 'test_start_density',
                'business_s': 'business_score',
                'future_pot': 'future_potential_score',
                'comprehens': 'comprehensive_score'
            }

            self.grid_gdf = self.grid_gdf.rename(columns=column_mapping)
            print("Column names fixed")
            print(f"Available columns: {self.grid_gdf.columns.tolist()}")

            return True
        except Exception as e:
            print(f"Error loading grid results: {e}")
            return False
    
    def create_beautiful_grid_heatmap(self, value_col, title, filename, color_scheme='business'):
        """创建美观的网格热力图"""
        print(f"Creating beautiful grid heatmap for {value_col}...")
        
        fig, ax = plt.subplots(figsize=(20, 16))
        
        # 创建自定义颜色映射
        colors = self.color_schemes[color_scheme]
        cmap = LinearSegmentedColormap.from_list("custom", colors, N=256)
        
        # 绘制网格热力图
        self.grid_gdf.plot(
            column=value_col,
            cmap=cmap,
            linewidth=0.05,
            ax=ax,
            edgecolor='white',
            alpha=0.85,
            legend=True,
            legend_kwds={
                'shrink': 0.8, 
                'aspect': 40,
                'label': value_col.replace('_', ' ').title()
            }
        )
        
        # 设置标题和标签
        ax.set_title(title, fontsize=24, fontweight='bold', pad=30)
        ax.set_xlabel('经度', fontsize=16, fontweight='bold')
        ax.set_ylabel('纬度', fontsize=16, fontweight='bold')
        
        # 美化坐标轴
        ax.tick_params(axis='both', which='major', labelsize=12)
        
        # 设置背景
        ax.set_facecolor('#f8f9fa')
        
        # 添加网格线
        ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)
        
        # 美化边框
        for spine in ax.spines.values():
            spine.set_linewidth(2)
            spine.set_color('#333333')
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/{filename}', dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.show()
    
    def create_interactive_folium_map(self):
        """创建交互式Folium地图"""
        print("Creating interactive Folium map...")

        # 计算地图中心
        bounds = self.grid_gdf.total_bounds
        center_lat = (bounds[1] + bounds[3]) / 2
        center_lon = (bounds[0] + bounds[2]) / 2

        # 创建地图
        m = folium.Map(
            location=[center_lat, center_lon],
            zoom_start=10,
            tiles=None
        )

        # 添加多种地图图层
        folium.TileLayer('OpenStreetMap', name='街道地图').add_to(m)
        folium.TileLayer('CartoDB positron', name='简洁地图').add_to(m)

        # 获取推荐网格
        top_grids = self.grid_gdf.nlargest(50, 'comprehensive_score')

        # 简化的网格热力图层 - 只显示高分网格
        def add_simplified_grid_layer(data, column, name, colormap):
            # 过滤高分网格
            high_score_grids = data[data[column] > data[column].quantile(0.9)]

            if len(high_score_grids) > 0:
                # 创建简化的GeoJson图层
                geojson = folium.GeoJson(
                    high_score_grids,
                    style_function=lambda feature: {
                        'fillColor': 'red' if feature['properties'].get(column, 0) > 80 else 'orange',
                        'color': 'white',
                        'weight': 1,
                        'fillOpacity': 0.6,
                    },
                    popup=folium.GeoJsonPopup(
                        fields=['grid_id', column, 'total_poi_count', 'weighted_flow_density'],
                        aliases=['网格ID', name, 'POI总数', '加权流量'],
                        localize=True
                    ),
                    tooltip=folium.GeoJsonTooltip(
                        fields=['grid_id', column],
                        aliases=['网格ID', name],
                        localize=True
                    )
                )

                # 添加到地图
                feature_group = folium.FeatureGroup(name=name)
                feature_group.add_child(geojson)
                m.add_child(feature_group)

        # 添加高分网格图层
        add_simplified_grid_layer(self.grid_gdf, 'comprehensive_score', '高分网格-综合评分', 'viridis')
        add_simplified_grid_layer(self.grid_gdf, 'business_score', '高分网格-商业价值', 'Reds')
        
        # 添加推荐位置标记
        recommendation_group = folium.FeatureGroup(name='推荐位置')
        
        for idx, row in top_grids.head(10).iterrows():
            # 根据评分选择颜色和图标
            if row['comprehensive_score'] > 90:
                color = 'red'
                icon = 'star'
            elif row['comprehensive_score'] > 80:
                color = 'orange'
                icon = 'heart'
            else:
                color = 'green'
                icon = 'info-sign'
            
            folium.Marker(
                location=[row['center_lat'], row['center_lon']],
                popup=folium.Popup(f"""
                <div style="width:250px">
                <h4>推荐网格 #{row['grid_id']}</h4>
                <hr>
                <b>综合评分:</b> {row['comprehensive_score']:.1f}<br>
                <b>商业价值:</b> {row['business_score']:.1f}<br>
                <b>未来潜力:</b> {row['future_potential_score']:.1f}<br>
                <b>POI总数:</b> {row['total_poi_count']:.0f}<br>
                <b>商业POI:</b> {row['business_poi_count']:.0f}<br>
                <b>加权流量:</b> {row['weighted_flow_density']:.0f}<br>
                <b>预测密度:</b> {row['predicted_density']:.0f}<br>
                <b>中心坐标:</b> ({row['center_lat']:.4f}, {row['center_lon']:.4f})
                </div>
                """, max_width=300),
                tooltip=f"推荐网格#{row['grid_id']} - 评分:{row['comprehensive_score']:.1f}",
                icon=folium.Icon(color=color, icon=icon)
            ).add_to(recommendation_group)
        
        m.add_child(recommendation_group)
        
        # 添加图层控制
        folium.LayerControl().add_to(m)
        
        # 添加全屏插件
        from folium.plugins import Fullscreen
        Fullscreen().add_to(m)
        
        # 保存地图
        m.save(f'{self.results_dir}/interactive_grid_map.html')
        print(f"Interactive grid map saved to {self.results_dir}/interactive_grid_map.html")
        
        return m
    
    def create_plotly_dashboard(self):
        """创建Plotly仪表板"""
        print("Creating Plotly dashboard...")
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=3,
            subplot_titles=[
                '综合评分分布', '商业价值 vs 未来潜力', '流量密度分析',
                '推荐位置地理分布', 'POI密度统计', '评分相关性分析'
            ],
            specs=[
                [{"type": "histogram"}, {"type": "scatter"}, {"type": "bar"}],
                [{"type": "scatter"}, {"type": "box"}, {"type": "heatmap"}]
            ]
        )
        
        # 1. 综合评分分布
        fig.add_trace(
            go.Histogram(
                x=self.grid_gdf['comprehensive_score'],
                nbinsx=50,
                name='综合评分分布',
                marker_color='skyblue'
            ),
            row=1, col=1
        )
        
        # 2. 商业价值 vs 未来潜力散点图
        top_100 = self.grid_gdf.nlargest(100, 'comprehensive_score')
        fig.add_trace(
            go.Scatter(
                x=top_100['business_score'],
                y=top_100['future_potential_score'],
                mode='markers',
                marker=dict(
                    size=top_100['comprehensive_score']/5,
                    color=top_100['comprehensive_score'],
                    colorscale='Viridis',
                    showscale=True
                ),
                text=[f'网格{gid}' for gid in top_100['grid_id']],
                name='前100网格'
            ),
            row=1, col=2
        )
        
        # 3. 流量密度分析
        top_20 = self.grid_gdf.nlargest(20, 'comprehensive_score')
        flow_types = ['start_density', 'end_density', 'predicted_density', 'test_start_density']
        flow_means = [top_20[col].mean() for col in flow_types]
        flow_labels = ['起点密度', '终点密度', '预测密度', '测试起点密度']
        
        fig.add_trace(
            go.Bar(
                x=flow_labels,
                y=flow_means,
                marker_color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
                name='平均流量密度'
            ),
            row=1, col=3
        )
        
        # 4. 推荐位置地理分布
        fig.add_trace(
            go.Scatter(
                x=top_20['center_lon'],
                y=top_20['center_lat'],
                mode='markers+text',
                marker=dict(
                    size=top_20['comprehensive_score']/4,
                    color=top_20['comprehensive_score'],
                    colorscale='Reds',
                    line=dict(width=2, color='white')
                ),
                text=[f'#{i+1}' for i in range(len(top_20))],
                textposition="middle center",
                name='推荐位置'
            ),
            row=2, col=1
        )
        
        # 5. POI密度统计
        poi_categories = ['total_poi_count', 'business_poi_count', 'transport_poi_count', 
                         'education_poi_count', 'medical_poi_count']
        poi_labels = ['总POI', '商业POI', '交通POI', '教育POI', '医疗POI']
        
        for i, (cat, label) in enumerate(zip(poi_categories, poi_labels)):
            fig.add_trace(
                go.Box(
                    y=top_20[cat],
                    name=label,
                    boxpoints='outliers'
                ),
                row=2, col=2
            )
        
        # 6. 评分相关性分析
        score_cols = ['business_score', 'future_potential_score', 'comprehensive_score']
        correlation_matrix = self.grid_gdf[score_cols].corr()
        
        fig.add_trace(
            go.Heatmap(
                z=correlation_matrix.values,
                x=['商业价值', '未来潜力', '综合评分'],
                y=['商业价值', '未来潜力', '综合评分'],
                colorscale='RdBu',
                zmid=0,
                text=correlation_matrix.values,
                texttemplate='%{text:.2f}',
                textfont={"size": 12}
            ),
            row=2, col=3
        )
        
        # 更新布局
        fig.update_layout(
            title={
                'text': '基于网格的商业选址分析仪表板',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 24, 'family': 'Arial Black'}
            },
            height=1000,
            showlegend=False
        )
        
        # 保存仪表板
        fig.write_html(f'{self.results_dir}/grid_dashboard.html')
        print(f"Grid dashboard saved to {self.results_dir}/grid_dashboard.html")
        
        return fig
    
    def generate_summary_report(self):
        """生成总结报告"""
        print("Generating summary report...")
        
        top_20 = self.grid_gdf.nlargest(20, 'comprehensive_score')
        
        report = f"""
# 基于网格的商业选址分析报告

## 分析概况
- **分析网格数量**: {len(self.grid_gdf):,} 个
- **网格大小**: 1km × 1km
- **推荐位置数**: {len(top_20)} 个
- **数据源**: 摩拜单车出行数据 + 北京市POI数据

## 顶级推荐位置

| 排名 | 网格ID | 中心坐标 | 综合评分 | 商业价值 | 未来潜力 | POI总数 | 加权流量 |
|------|--------|----------|----------|----------|----------|---------|----------|
"""
        
        for i, (_, row) in enumerate(top_20.head(10).iterrows()):
            report += f"| {i+1} | {row['grid_id']} | ({row['center_lat']:.4f}, {row['center_lon']:.4f}) | {row['comprehensive_score']:.1f} | {row['business_score']:.1f} | {row['future_potential_score']:.1f} | {row['total_poi_count']:.0f} | {row['weighted_flow_density']:.0f} |\n"
        
        report += f"""

## 关键发现

### 1. 空间分布特征
- 高价值网格主要集中在北京市中心城区
- 朝阳、海淀、东城、西城区域表现突出
- 五环内网格整体评分较高

### 2. 评分分析
- **最高综合评分**: {top_20['comprehensive_score'].max():.1f}
- **平均综合评分**: {top_20['comprehensive_score'].mean():.1f}
- **评分标准差**: {top_20['comprehensive_score'].std():.1f}

### 3. 流量特征
- **平均加权流量**: {top_20['weighted_flow_density'].mean():.0f}
- **最高流量网格**: 网格{top_20.loc[top_20['weighted_flow_density'].idxmax(), 'grid_id']} ({top_20['weighted_flow_density'].max():.0f})
- **预测流量占比**: {(top_20['predicted_density'].sum() / top_20['weighted_flow_density'].sum() * 100):.1f}%

### 4. POI分布
- **平均POI密度**: {top_20['total_poi_count'].mean():.0f} 个/网格
- **商业POI占比**: {(top_20['business_poi_count'].sum() / top_20['total_poi_count'].sum() * 100):.1f}%
- **交通设施覆盖**: {(top_20['transport_poi_count'] > 0).sum()} 个网格有交通设施

## 投资建议

### 优先级分类
- **A级推荐** (综合评分>90): {len(top_20[top_20['comprehensive_score'] > 90])} 个网格
- **B级推荐** (综合评分80-90): {len(top_20[(top_20['comprehensive_score'] >= 80) & (top_20['comprehensive_score'] <= 90)])} 个网格
- **C级推荐** (综合评分70-80): {len(top_20[(top_20['comprehensive_score'] >= 70) & (top_20['comprehensive_score'] < 80)])} 个网格

### 投资策略
1. **短期收益**: 关注商业价值评分高的网格
2. **长期布局**: 重视未来潜力评分高的网格
3. **平衡投资**: 选择综合评分高的网格

## 生成文件
- `interactive_grid_map.html`: 交互式网格地图
- `grid_dashboard.html`: 综合分析仪表板
- `grid_*.png`: 各类热力图
- `grid_analysis_results.shp`: 完整网格分析结果
"""
        
        # 保存报告
        with open(f'{self.results_dir}/grid_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"Summary report saved to {self.results_dir}/grid_analysis_report.md")

def main():
    """主函数"""
    # 网格分析结果路径
    grid_results_path = 'results/grid_analysis/grid_analysis_results.shp'
    
    # 创建可视化对象
    viz = GridVisualization(grid_results_path)
    
    # 加载数据
    if not viz.load_grid_results():
        print("Failed to load grid results")
        return
    
    # 创建美观的网格热力图
    viz.create_beautiful_grid_heatmap(
        'comprehensive_score', 
        '基于网格的综合评分热力图', 
        'grid_comprehensive_beautiful.png',
        'comprehensive'
    )
    
    viz.create_beautiful_grid_heatmap(
        'business_score', 
        '基于网格的商业价值热力图', 
        'grid_business_beautiful.png',
        'business'
    )
    
    viz.create_beautiful_grid_heatmap(
        'future_potential_score', 
        '基于网格的未来潜力热力图', 
        'grid_future_beautiful.png',
        'potential'
    )
    
    viz.create_beautiful_grid_heatmap(
        'weighted_flow_density', 
        '基于网格的加权流量密度热力图', 
        'grid_flow_beautiful.png',
        'flow'
    )
    
    # 创建交互式地图
    viz.create_interactive_folium_map()
    
    # 创建Plotly仪表板
    viz.create_plotly_dashboard()
    
    # 生成总结报告
    viz.generate_summary_report()
    
    print("All grid visualizations completed!")
    print(f"Results saved in: {viz.results_dir}")

if __name__ == "__main__":
    main()
