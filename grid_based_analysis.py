import pandas as pd
import numpy as np
import geopandas as gpd
from shapely.geometry import Point
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
import folium
from folium.plugins import HeatMap
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class GridBasedAnalysis:
    """基于网格shp文件的商业选址分析"""
    
    def __init__(self, grid_shp_path):
        self.grid_shp_path = grid_shp_path
        self.grid_gdf = None
        self.poi_data = None
        self.bike_flow_data = None
        self.results_dir = 'results/grid_analysis'
        self.ensure_results_dir()
        
    def ensure_results_dir(self):
        """确保结果目录存在"""
        import os
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
    
    def load_grid_data(self):
        """加载网格shp文件"""
        print("Loading grid shapefile...")
        try:
            self.grid_gdf = gpd.read_file(self.grid_shp_path)
            print(f"Grid loaded: {len(self.grid_gdf)} cells")
            print(f"Grid CRS: {self.grid_gdf.crs}")
            
            # 如果是3857投影，转换为4326地理坐标系
            if self.grid_gdf.crs.to_epsg() == 3857:
                print("Converting from EPSG:3857 to EPSG:4326...")
                self.grid_gdf = self.grid_gdf.to_crs('EPSG:4326')
            
            # 计算网格中心点
            self.grid_gdf['center_lat'] = self.grid_gdf.geometry.centroid.y
            self.grid_gdf['center_lon'] = self.grid_gdf.geometry.centroid.x
            
            # 添加网格ID
            self.grid_gdf['grid_id'] = range(len(self.grid_gdf))
            
            print(f"Grid bounds: {self.grid_gdf.total_bounds}")
            return True
            
        except Exception as e:
            print(f"Error loading grid file: {e}")
            return False
    
    def load_business_data(self):
        """加载POI和单车流量数据"""
        print("Loading business data...")
        
        # 加载POI数据
        try:
            self.poi_data = pd.read_csv('北京市POI数据.csv')
            print(f"POI data loaded: {len(self.poi_data)} records")
        except FileNotFoundError:
            print("POI data not found")
            return False
        
        # 加载单车流量数据
        try:
            predictions = pd.read_csv('destination_predictions.csv')
            train_data = pd.read_csv('processed_train.csv')
            test_data = pd.read_csv('processed_test.csv')
            
            print(f"Loaded predictions: {len(predictions)} records")
            print(f"Loaded train data: {len(train_data)} records")
            print(f"Loaded test data: {len(test_data)} records")
            
            # 采样并合并数据
            train_sample_size = min(200000, len(train_data))
            train_sample = train_data.sample(n=train_sample_size, random_state=42)
            
            # 合并所有流量数据
            start_points = train_sample[['geohashed_start_loc_lat', 'geohashed_start_loc_lon']].copy()
            start_points.columns = ['lat', 'lon']
            start_points['type'] = 'start'
            start_points['weight'] = 1.0
            
            end_points = train_sample[['geohashed_end_loc_lat', 'geohashed_end_loc_lon']].copy()
            end_points.columns = ['lat', 'lon']
            end_points['type'] = 'end'
            end_points['weight'] = 1.0
            
            # 预测数据
            pred_sample_size = min(100000, len(predictions))
            pred_sample = predictions.sample(n=pred_sample_size, random_state=42)
            pred_points = pred_sample[['predicted_lat', 'predicted_lon']].copy()
            pred_points.columns = ['lat', 'lon']
            pred_points['type'] = 'predicted'
            pred_points['weight'] = 1.5
            
            # 测试起点
            test_sample_size = min(50000, len(test_data))
            test_sample = test_data.sample(n=test_sample_size, random_state=42)
            test_start_points = test_sample[['geohashed_start_loc_lat', 'geohashed_start_loc_lon']].copy()
            test_start_points.columns = ['lat', 'lon']
            test_start_points['type'] = 'test_start'
            test_start_points['weight'] = 1.2
            
            self.bike_flow_data = pd.concat([start_points, end_points, pred_points, test_start_points], ignore_index=True)
            print(f"Total bike flow data: {len(self.bike_flow_data)} records")
            
            return True
            
        except FileNotFoundError as e:
            print(f"Flow data not found: {e}")
            return False
    
    def spatial_join_analysis(self):
        """使用空间连接进行网格分析"""
        print("Performing spatial join analysis...")
        
        # 初始化结果列
        self.grid_gdf['total_poi_count'] = 0
        self.grid_gdf['business_poi_count'] = 0
        self.grid_gdf['transport_poi_count'] = 0
        self.grid_gdf['education_poi_count'] = 0
        self.grid_gdf['medical_poi_count'] = 0
        self.grid_gdf['bike_flow_density'] = 0
        self.grid_gdf['weighted_flow_density'] = 0
        self.grid_gdf['start_density'] = 0
        self.grid_gdf['end_density'] = 0
        self.grid_gdf['predicted_density'] = 0
        self.grid_gdf['test_start_density'] = 0
        
        # 1. POI空间连接
        print("Processing POI spatial join...")
        poi_gdf = gpd.GeoDataFrame(
            self.poi_data,
            geometry=gpd.points_from_xy(self.poi_data['经度'], self.poi_data['纬度']),
            crs='EPSG:4326'
        )
        
        # 空间连接POI和网格
        poi_grid_join = gpd.sjoin(poi_gdf, self.grid_gdf, how='left', predicate='within')
        
        # 统计每个网格的POI数量
        poi_stats = poi_grid_join.groupby('grid_id').agg({
            '名称': 'count',  # 总POI数量
            '大类': lambda x: sum(x.isin(['购物服务', '餐饮服务', '生活服务', '金融保险服务', '公司企业'])),  # 商业POI
        }).rename(columns={'名称': 'total_poi_count', '大类': 'business_poi_count'})
        
        # 分别统计各类POI
        category_mapping = {
            'transport_poi_count': ['交通设施'],
            'education_poi_count': ['科教文化服务'],
            'medical_poi_count': ['医疗保健服务']
        }
        
        for col_name, categories in category_mapping.items():
            category_stats = poi_grid_join.groupby('grid_id')['大类'].apply(
                lambda x: sum(x.isin(categories))
            ).rename(col_name)
            poi_stats = poi_stats.join(category_stats, how='outer')
        
        # 填充POI统计到网格
        for col in poi_stats.columns:
            self.grid_gdf.loc[poi_stats.index, col] = poi_stats[col].fillna(0)
        
        # 2. 单车流量空间连接
        print("Processing bike flow spatial join...")
        flow_gdf = gpd.GeoDataFrame(
            self.bike_flow_data,
            geometry=gpd.points_from_xy(self.bike_flow_data['lon'], self.bike_flow_data['lat']),
            crs='EPSG:4326'
        )
        
        # 空间连接流量数据和网格
        flow_grid_join = gpd.sjoin(flow_gdf, self.grid_gdf, how='left', predicate='within')
        
        # 统计每个网格的流量密度
        flow_stats = flow_grid_join.groupby('grid_id').agg({
            'type': 'count',  # 总流量
            'weight': 'sum',  # 加权流量
        }).rename(columns={'type': 'bike_flow_density', 'weight': 'weighted_flow_density'})
        
        # 分别统计各类型流量
        type_stats = flow_grid_join.groupby(['grid_id', 'type']).size().unstack(fill_value=0)
        for col in ['start_density', 'end_density', 'predicted_density', 'test_start_density']:
            type_name = col.replace('_density', '')
            if type_name in type_stats.columns:
                flow_stats[col] = type_stats[type_name]
            else:
                flow_stats[col] = 0
        
        # 填充流量统计到网格
        for col in flow_stats.columns:
            self.grid_gdf.loc[flow_stats.index, col] = flow_stats[col].fillna(0)
        
        print("Spatial join analysis completed")
    
    def calculate_scores(self):
        """计算各种评分"""
        print("Calculating business scores...")
        
        from sklearn.preprocessing import StandardScaler
        
        # 标准化特征
        scaler = StandardScaler()
        
        # 商业价值评分特征
        business_features = [
            'total_poi_count', 'business_poi_count', 'transport_poi_count',
            'education_poi_count', 'medical_poi_count', 'weighted_flow_density',
            'predicted_density'
        ]
        
        # 确保所有特征列存在
        for feature in business_features:
            if feature not in self.grid_gdf.columns:
                self.grid_gdf[feature] = 0
        
        # 标准化
        scaled_features = scaler.fit_transform(self.grid_gdf[business_features])
        scaled_df = pd.DataFrame(scaled_features, columns=business_features)
        
        # 计算商业价值评分
        business_weights = {
            'total_poi_count': 0.12,
            'business_poi_count': 0.20,
            'transport_poi_count': 0.12,
            'education_poi_count': 0.08,
            'medical_poi_count': 0.08,
            'weighted_flow_density': 0.25,
            'predicted_density': 0.15
        }
        
        self.grid_gdf['business_score'] = 0
        for feature, weight in business_weights.items():
            self.grid_gdf['business_score'] += scaled_df[feature] * weight
        
        # 计算未来潜力评分
        future_weights = {
            'predicted_density': 0.4,
            'test_start_density': 0.3,
            'business_poi_count': 0.2,
            'transport_poi_count': 0.1
        }
        
        self.grid_gdf['future_potential_score'] = 0
        for feature, weight in future_weights.items():
            if feature in scaled_df.columns:
                self.grid_gdf['future_potential_score'] += scaled_df[feature] * weight
        
        # 综合评分
        self.grid_gdf['comprehensive_score'] = (
            self.grid_gdf['business_score'] * 0.7 + 
            self.grid_gdf['future_potential_score'] * 0.3
        )
        
        # 归一化到0-100分
        for score_col in ['business_score', 'future_potential_score', 'comprehensive_score']:
            min_score = self.grid_gdf[score_col].min()
            max_score = self.grid_gdf[score_col].max()
            if max_score > min_score:
                self.grid_gdf[score_col] = ((self.grid_gdf[score_col] - min_score) / (max_score - min_score)) * 100
            else:
                self.grid_gdf[score_col] = 50
        
        print("Score calculation completed")
    
    def create_grid_heatmap(self, value_col, title, filename, cmap='viridis'):
        """创建基于网格的热力图"""
        print(f"Creating grid heatmap for {value_col}...")
        
        fig, ax = plt.subplots(figsize=(16, 12))
        
        # 绘制网格热力图
        self.grid_gdf.plot(
            column=value_col,
            cmap=cmap,
            linewidth=0.1,
            ax=ax,
            edgecolor='white',
            alpha=0.8,
            legend=True,
            legend_kwds={'shrink': 0.8, 'aspect': 30}
        )
        
        # 设置标题和标签
        ax.set_title(title, fontsize=20, fontweight='bold', pad=20)
        ax.set_xlabel('经度', fontsize=14, fontweight='bold')
        ax.set_ylabel('纬度', fontsize=14, fontweight='bold')
        
        # 移除坐标轴刻度
        ax.set_xticks([])
        ax.set_yticks([])
        
        # 设置背景
        ax.set_facecolor('lightgray')
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/{filename}', dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.show()
    
    def save_results(self):
        """保存分析结果"""
        print("Saving analysis results...")
        
        # 保存网格分析结果
        self.grid_gdf.to_file(f'{self.results_dir}/grid_analysis_results.shp')
        
        # 保存CSV格式
        result_df = self.grid_gdf.drop('geometry', axis=1)
        result_df.to_csv(f'{self.results_dir}/grid_analysis_results.csv', index=False)
        
        # 获取推荐位置
        recommendations = self.grid_gdf.nlargest(20, 'comprehensive_score')
        recommendations_df = recommendations.drop('geometry', axis=1)
        recommendations_df.to_csv(f'{self.results_dir}/top_grid_recommendations.csv', index=False)
        
        print(f"Results saved to {self.results_dir}")
        
        return recommendations

def main():
    """主函数"""
    # 网格文件路径
    grid_shp_path = r"D:\桌面\比赛文件\商业选址\SAU\SAU\Grid\BeijingGrid_1km_3857.shp"
    
    # 创建分析对象
    analyzer = GridBasedAnalysis(grid_shp_path)
    
    # 加载数据
    if not analyzer.load_grid_data():
        print("Failed to load grid data")
        return
    
    if not analyzer.load_business_data():
        print("Failed to load business data")
        return
    
    # 执行空间分析
    analyzer.spatial_join_analysis()
    
    # 计算评分
    analyzer.calculate_scores()
    
    # 创建热力图
    analyzer.create_grid_heatmap(
        'business_score', 
        '基于网格的商业价值热力图', 
        'grid_business_heatmap.png',
        'Reds'
    )
    
    analyzer.create_grid_heatmap(
        'future_potential_score', 
        '基于网格的未来潜力热力图', 
        'grid_future_heatmap.png',
        'Blues'
    )
    
    analyzer.create_grid_heatmap(
        'comprehensive_score', 
        '基于网格的综合评分热力图', 
        'grid_comprehensive_heatmap.png',
        'viridis'
    )
    
    analyzer.create_grid_heatmap(
        'weighted_flow_density', 
        '基于网格的加权流量密度热力图', 
        'grid_flow_heatmap.png',
        'plasma'
    )
    
    # 保存结果
    recommendations = analyzer.save_results()
    
    # 显示推荐结果
    print("\n=== 基于网格的商业选址推荐 ===")
    print(f"分析了 {len(analyzer.grid_gdf)} 个网格")
    print(f"推荐了 {len(recommendations)} 个优质位置")
    
    print("\n前10个推荐位置:")
    for idx, row in recommendations.head(10).iterrows():
        print(f"网格 {row['grid_id']}: 中心点 ({row['center_lat']:.4f}, {row['center_lon']:.4f})")
        print(f"  综合评分: {row['comprehensive_score']:.2f}")
        print(f"  商业评分: {row['business_score']:.2f}")
        print(f"  未来潜力: {row['future_potential_score']:.2f}")
        print(f"  POI总数: {row['total_poi_count']:.0f}")
        print(f"  加权流量: {row['weighted_flow_density']:.0f}")
        print()
    
    print("Analysis completed!")
    return analyzer

if __name__ == "__main__":
    analyzer = main()
