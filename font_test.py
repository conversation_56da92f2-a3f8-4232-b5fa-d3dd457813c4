"""
中文字体测试脚本
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np

# 设置中文字体
def setup_chinese_font():
    """设置中文字体"""
    # 获取系统字体列表
    font_list = [f.name for f in fm.fontManager.ttflist]
    
    # 优先选择的中文字体
    chinese_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong', 'Arial Unicode MS']
    
    for font in chinese_fonts:
        if font in font_list:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False
            plt.rcParams['font.size'] = 12
            print(f"Using Chinese font: {font}")
            return font
    
    # 如果没有找到中文字体，使用默认字体
    print("Warning: No Chinese font found, using default font")
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    return 'DejaVu Sans'

# 设置字体
font_name = setup_chinese_font()

# 创建测试图表
fig, axes = plt.subplots(1, 2, figsize=(16, 6))
fig.suptitle('中文字体显示测试', fontsize=20, fontweight='bold')

# 测试1：柱状图
categories = ['商业价值', '未来潜力', '综合评分', '流量密度']
values = [85, 78, 92, 67]

bars = axes[0].bar(categories, values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
axes[0].set_title('评分指标测试', fontsize=16, fontweight='bold')
axes[0].set_ylabel('评分值', fontsize=14)

# 添加数值标签
for bar in bars:
    height = bar.get_height()
    axes[0].text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height}', ha='center', va='bottom', fontweight='bold')

# 测试2：饼图
poi_types = ['购物服务', '餐饮服务', '生活服务', '交通设施', '其他']
poi_counts = [25, 20, 18, 15, 22]

axes[1].pie(poi_counts, labels=poi_types, autopct='%1.1f%%', startangle=90)
axes[1].set_title('POI类型分布测试', fontsize=16, fontweight='bold')

# 美化图表
for ax in axes:
    ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('results/ultimate_viz_fixed/font_test.png', dpi=300, bbox_inches='tight', facecolor='white')
plt.show()

print(f"Font test completed using: {font_name}")
print("Check the generated image to verify Chinese characters display correctly.")
