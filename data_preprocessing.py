import pandas as pd
import numpy as np
import geohash2 as geohash
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class MobikeDataProcessor:
    """摩拜单车数据预处理类"""
    
    def __init__(self):
        self.train_data = None
        self.test_data = None
        self.poi_data = None
        
    def load_data(self):
        """加载数据"""
        print("Loading data...")
        self.train_data = pd.read_csv('train.csv')
        self.test_data = pd.read_csv('test.csv')
        self.poi_data = pd.read_csv('北京市POI数据.csv')
        print(f"Train data shape: {self.train_data.shape}")
        print(f"Test data shape: {self.test_data.shape}")
        print(f"POI data shape: {self.poi_data.shape}")
        
    def decode_geohash(self, geohash_str):
        """解码geohash为经纬度"""
        try:
            lat, lon = geohash.decode(geohash_str)
            return lat, lon
        except:
            return None, None
    
    def extract_time_features(self, df):
        """提取时间特征"""
        df['starttime'] = pd.to_datetime(df['starttime'])
        df['hour'] = df['starttime'].dt.hour
        df['day_of_week'] = df['starttime'].dt.dayofweek
        df['is_weekend'] = df['day_of_week'].isin([5, 6]).astype(int)
        df['is_rush_hour'] = ((df['hour'].between(7, 9)) | (df['hour'].between(17, 19))).astype(int)
        return df
    
    def process_geohash_data(self, df, location_col):
        """处理geohash数据，提取经纬度"""
        print(f"Processing geohash data for {location_col}...")

        # 解码geohash
        coords = df[location_col].apply(lambda x: self.decode_geohash(x) if pd.notna(x) else (None, None))
        df[f'{location_col}_lat'] = coords.apply(lambda x: x[0])
        df[f'{location_col}_lon'] = coords.apply(lambda x: x[1])

        # 确保坐标是数值类型
        df[f'{location_col}_lat'] = pd.to_numeric(df[f'{location_col}_lat'], errors='coerce')
        df[f'{location_col}_lon'] = pd.to_numeric(df[f'{location_col}_lon'], errors='coerce')

        # 移除无效坐标
        valid_mask = (df[f'{location_col}_lat'].notna()) & (df[f'{location_col}_lon'].notna())
        df = df[valid_mask].copy()

        print(f"Valid coordinates: {len(df)} / {len(coords)}")
        return df
    
    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """计算两点间距离（公里）"""
        from math import radians, cos, sin, asin, sqrt
        
        # 转换为弧度
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        
        # haversine公式
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371  # 地球半径（公里）
        
        return c * r
    
    def add_distance_features(self, df):
        """添加距离相关特征"""
        if 'geohashed_end_loc_lat' in df.columns:
            # 确保坐标列是数值类型
            coord_cols = ['geohashed_start_loc_lat', 'geohashed_start_loc_lon',
                         'geohashed_end_loc_lat', 'geohashed_end_loc_lon']
            for col in coord_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # 移除包含NaN的行
            df = df.dropna(subset=coord_cols)

            df['trip_distance'] = df.apply(
                lambda row: self.calculate_distance(
                    row['geohashed_start_loc_lat'], row['geohashed_start_loc_lon'],
                    row['geohashed_end_loc_lat'], row['geohashed_end_loc_lon']
                ), axis=1
            )
        return df
    
    def process_train_data(self):
        """处理训练数据"""
        print("Processing training data...")
        
        # 提取时间特征
        self.train_data = self.extract_time_features(self.train_data)
        
        # 处理起点geohash
        self.train_data = self.process_geohash_data(self.train_data, 'geohashed_start_loc')
        
        # 处理终点geohash
        self.train_data = self.process_geohash_data(self.train_data, 'geohashed_end_loc')
        
        # 添加距离特征
        self.train_data = self.add_distance_features(self.train_data)
        
        print(f"Processed train data shape: {self.train_data.shape}")
        return self.train_data
    
    def process_test_data(self):
        """处理测试数据"""
        print("Processing test data...")
        
        # 提取时间特征
        self.test_data = self.extract_time_features(self.test_data)
        
        # 处理起点geohash
        self.test_data = self.process_geohash_data(self.test_data, 'geohashed_start_loc')
        
        print(f"Processed test data shape: {self.test_data.shape}")
        return self.test_data
    
    def get_poi_features(self, lat, lon, radius=0.5):
        """获取指定位置周围的POI特征"""
        # 计算距离
        distances = self.poi_data.apply(
            lambda row: self.calculate_distance(lat, lon, row['纬度'], row['经度']), 
            axis=1
        )
        
        # 筛选半径内的POI
        nearby_pois = self.poi_data[distances <= radius]
        
        # 统计各类POI数量
        poi_counts = nearby_pois['大类'].value_counts().to_dict()
        
        # 标准化特征名称
        feature_dict = {}
        for category, count in poi_counts.items():
            feature_dict[f'poi_{category}_count'] = count
            
        return feature_dict
    
    def save_processed_data(self):
        """保存处理后的数据"""
        if self.train_data is not None:
            self.train_data.to_csv('processed_train.csv', index=False)
            print("Saved processed_train.csv")
            
        if self.test_data is not None:
            self.test_data.to_csv('processed_test.csv', index=False)
            print("Saved processed_test.csv")

if __name__ == "__main__":
    processor = MobikeDataProcessor()
    processor.load_data()
    processor.process_train_data()
    processor.process_test_data()
    processor.save_processed_data()
