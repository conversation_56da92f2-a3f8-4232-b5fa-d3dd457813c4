import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler, LabelEncoder
import joblib
import warnings
warnings.filterwarnings('ignore')

class DestinationPredictor:
    """摩拜单车终点预测模型"""
    
    def __init__(self):
        self.lat_model = None
        self.lon_model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_columns = []
        
    def prepare_features(self, df):
        """准备特征数据"""
        features = df.copy()
        
        # 基础特征
        #hour表示小时
        #day_of_week表示星期几
        #is_weekend表示是否是周末
        #is_rush_hour表示是否是 rush hour 时间段
        feature_cols = [
            'userid', 'bikeid', 'biketype', 'hour', 'day_of_week', 
            'is_weekend', 'is_rush_hour', 'geohashed_start_loc_lat', 
            'geohashed_start_loc_lon'
        ]
        
        # 用户行为特征
        #groupby()方法用于根据指定的列进行分组
        #agg()方法用于对分组后的数据进行聚合操作
        #fillna(0)方法用于将缺失值填充为0
        if 'userid' in features.columns:
            user_stats = features.groupby('userid').agg({
                'geohashed_start_loc_lat': ['mean', 'std'],
                'geohashed_start_loc_lon': ['mean', 'std'],
                'hour': 'mean'
            }).fillna(0)
            
            user_stats.columns = ['user_lat_mean', 'user_lat_std', 'user_lon_mean', 'user_lon_std', 'user_hour_mean']
            features = features.merge(user_stats, left_on='userid', right_index=True, how='left')
            
            feature_cols.extend(['user_lat_mean', 'user_lat_std', 'user_lon_mean', 'user_lon_std', 'user_hour_mean'])
        
        # 位置聚类特征
        #round()方法用于将数值四舍五入到指定的小数位数
        #3表示保留3位小数
        features['start_lat_rounded'] = features['geohashed_start_loc_lat'].round(3)
        features['start_lon_rounded'] = features['geohashed_start_loc_lon'].round(3)
        
        # 编码分类特征
        #LabelEncoder()方法用于将分类特征转换为数值特征
        #fit_transform()方法用于拟合数据并转换数据
        #transform()方法用于转换数据
        categorical_cols = ['biketype']
        for col in categorical_cols:
            if col in features.columns:
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    features[f'{col}_encoded'] = self.label_encoders[col].fit_transform(features[col].astype(str))
                else:
                    features[f'{col}_encoded'] = self.label_encoders[col].transform(features[col].astype(str))
                feature_cols.append(f'{col}_encoded')
        
        # 时间周期特征
        #np.sin()方法用于计算正弦值
        #np.cos()方法用于计算余弦值
        #2 * np.pi * features['hour'] / 24表示将24小时转换为2 * np.pi弧度
        #2 * np.pi * features['day_of_week'] / 7表示将7天转换为2 * np.pi弧度
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        features['dow_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
        features['dow_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
        
        feature_cols.extend(['hour_sin', 'hour_cos', 'dow_sin', 'dow_cos'])
        
        # 选择最终特征
        #available_cols表示可用的特征列
        available_cols = [col for col in feature_cols if col in features.columns]
        self.feature_columns = available_cols
        
        return features[available_cols].fillna(0)
    
    def train_model(self, train_data):
        """训练预测模型"""
        print("Preparing training features...")
        X = self.prepare_features(train_data)
        #y_lat表示终点纬度
        #y_lon表示终点经度
        y_lat = train_data['geohashed_end_loc_lat']
        y_lon = train_data['geohashed_end_loc_lon']
        
        print(f"Feature shape: {X.shape}")
        print(f"Features: {self.feature_columns}")
        
        # 分割训练和验证集
        #train_test_split()方法用于将数据集分割为训练集和验证集
        #test_size=0.2表示将数据集分割为80%的训练集和20%的验证集
        #random_state=42表示随机种子，用于复现结果
        X_train, X_val, y_lat_train, y_lat_val, y_lon_train, y_lon_val = train_test_split(
            X, y_lat, y_lon, test_size=0.2, random_state=42
        )
        
        # 标准化特征
        #StandardScaler()方法用于将特征转换为标准正态分布
        #fit_transform()方法用于拟合数据并转换数据
        #transform()方法用于转换数据
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        print("正在训练纬度模型...")
        #lat_model表示纬度模型
        #GradientBoostingRegressor()方法用于构建梯度提升回归模型
        #n_estimators=100表示构建100个决策树
        #learning_rate=0.1表示学习率为0.1
        #max_depth=6表示决策树的最大深度为6
        #random_state=42表示随机种子，用于复现结果
        #subsample=0.8表示每个决策树使用80%的样本进行训练
        self.lat_model = GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42,
            subsample=0.8
        )
        #fit()方法用于训练模型
        self.lat_model.fit(X_train_scaled, y_lat_train)
        
        print("Training longitude model...")
        #lon_model表示经度模型
        self.lon_model = GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42,
            subsample=0.8
        )
        self.lon_model.fit(X_train_scaled, y_lon_train)
        
        # 验证模型
        print("正在验证模型...")
        #验证模型的预测结果
        lat_pred = self.lat_model.predict(X_val_scaled)
        lon_pred = self.lon_model.predict(X_val_scaled)

        #验证模型的均方误差
        lat_mse = mean_squared_error(y_lat_val, lat_pred)
        lon_mse = mean_squared_error(y_lon_val, lon_pred)
        #验证模型的平均绝对误差
        lat_mae = mean_absolute_error(y_lat_val, lat_pred)
        lon_mae = mean_absolute_error(y_lon_val, lon_pred)
        
        print(f"验证结果:")
        print(f"纬度 - MSE: {lat_mse:.6f}, MAE: {lat_mae:.6f}")
        print(f"经度 - MSE: {lon_mse:.6f}, MAE: {lon_mae:.6f}")
        
        # 特征重要性
        #feature_importances_属性用于获取特征重要性
        lat_importance = pd.DataFrame({
            'feature': self.feature_columns,
            'importance': self.lat_model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\n前10个纬度模型的特征重要性:")
        print(lat_importance.head(10))
        
        #经度模型的特征重要性
        lon_importance = pd.DataFrame({
            'feature': self.feature_columns,
            'importance': self.lon_model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\n前10个经度模型的特征重要性:")
        print(lon_importance.head(10))
        
        return {
            'lat_mse': lat_mse,
            'lon_mse': lon_mse,
            'lat_mae': lat_mae,
            'lon_mae': lon_mae,
            'lat_importance': lat_importance,
            'lon_importance': lon_importance
        }
    
    def predict(self, test_data):
        """预测终点位置"""
        print("正在准备测试集特征...")
        
        X_test = self.prepare_features(test_data)
        X_test_scaled = self.scaler.transform(X_test)
        
        print("正在预测测试集终点位置...")
        #预测测试集的纬度
        lat_pred = self.lat_model.predict(X_test_scaled)
        #预测测试集的经度
        lon_pred = self.lon_model.predict(X_test_scaled)
        
        # 创建预测结果
        print("正在创建预测结果...")
        predictions = test_data[['orderid']].copy()
        predictions['predicted_lat'] = lat_pred
        predictions['predicted_lon'] = lon_pred
        #将预测结果转换为geohash
        predictions['predicted_geohash'] = predictions.apply(
            lambda row: self.encode_geohash(row['predicted_lat'], row['predicted_lon']),
            axis=1
        )
        
        return predictions
    
    def save_model(self, model_path='destination_model.pkl'):
        """保存模型"""
        print("正在保存模型...")
        model_data = {
            'lat_model': self.lat_model,
            'lon_model': self.lon_model,
            'scaler': self.scaler,
            'label_encoders': self.label_encoders,
            'feature_columns': self.feature_columns,
        }
        joblib.dump(model_data, model_path)
        print(f"模型已保存到 {model_path}")
    
    def load_model(self, model_path='destination_model.pkl'):
        """加载模型"""
        print("正在加载模型...")
        model_data = joblib.load(model_path)
        self.lat_model = model_data['lat_model']
        self.lon_model = model_data['lon_model']
        self.scaler = model_data['scaler']
        self.label_encoders = model_data['label_encoders']
        self.feature_columns = model_data['feature_columns']
        print(f"模型已从 {model_path} 加载")

def main():
    """主函数"""
    # 加载处理后的数据
    print("Loading processed data...")
    train_data = pd.read_csv('processed_train.csv')
    test_data = pd.read_csv('processed_test.csv')
    
    # 采样训练数据（如果数据太大）
    if len(train_data) > 500000:
        print("正在采样训练数据...")
        train_data = train_data.sample(n=500000, random_state=42)
    
    # 训练模型
    predictor = DestinationPredictor()
    metrics = predictor.train_model(train_data)
    
    # 保存模型
    predictor.save_model()
    
    # 预测测试集
    predictions = predictor.predict(test_data)
    
    # 保存预测结果
    predictions.to_csv('destination_predictions.csv', index=False)
    print("预测结果已保存到 destination_predictions.csv")
    
    return predictions

if __name__ == "__main__":
    predictions = main()
