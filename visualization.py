import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
import folium
from folium.plugins import HeatMap
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class BusinessLocationVisualizer:
    """商业选址可视化类"""
    
    def __init__(self):
        self.results_dir = 'results'
        self.ensure_results_dir()
    
    def ensure_results_dir(self):
        """确保结果目录存在"""
        import os
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
        
        # 创建子目录
        subdirs = ['heatmaps', 'recommendations', 'analysis']
        for subdir in subdirs:
            path = os.path.join(self.results_dir, subdir)
            if not os.path.exists(path):
                os.makedirs(path)
    
    def plot_business_score_heatmap(self, grid_df):
        """绘制商业评分热力图"""
        print("Creating business score heatmap...")
        
        # 创建透视表
        pivot_data = grid_df.pivot_table(
            values='business_score',
            index='grid_lat',
            columns='grid_lon',
            aggfunc='mean'
        )
        
        plt.figure(figsize=(15, 12))
        
        # 创建热力图
        sns.heatmap(
            pivot_data,
            cmap='YlOrRd',
            cbar_kws={'label': '商业价值评分'},
            xticklabels=False,
            yticklabels=False
        )
        
        plt.title('北京市商业选址价值热力图', fontsize=16, fontweight='bold')
        plt.xlabel('经度', fontsize=12)
        plt.ylabel('纬度', fontsize=12)
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/heatmaps/business_score_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_poi_distribution(self, poi_data):
        """绘制POI分布图"""
        print("Creating POI distribution plot...")
        
        # POI类别统计
        poi_counts = poi_data['大类'].value_counts().head(10)
        
        plt.figure(figsize=(12, 8))
        bars = plt.bar(range(len(poi_counts)), poi_counts.values, color='skyblue', alpha=0.8)
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{int(height):,}', ha='center', va='bottom', fontsize=10)
        
        plt.title('北京市POI分布统计', fontsize=16, fontweight='bold')
        plt.xlabel('POI类别', fontsize=12)
        plt.ylabel('数量', fontsize=12)
        plt.xticks(range(len(poi_counts)), poi_counts.index, rotation=45, ha='right')
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/analysis/poi_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_bike_flow_heatmap(self, grid_df):
        """绘制单车流量热力图"""
        if 'bike_flow_density' not in grid_df.columns:
            print("No bike flow data available for visualization")
            return
        
        print("Creating bike flow heatmap...")
        
        # 创建透视表
        pivot_data = grid_df.pivot_table(
            values='bike_flow_density',
            index='grid_lat',
            columns='grid_lon',
            aggfunc='mean'
        )
        
        plt.figure(figsize=(15, 12))
        
        # 创建热力图
        sns.heatmap(
            pivot_data,
            cmap='Blues',
            cbar_kws={'label': '单车流量密度'},
            xticklabels=False,
            yticklabels=False
        )
        
        plt.title('北京市摩拜单车流量热力图', fontsize=16, fontweight='bold')
        plt.xlabel('经度', fontsize=12)
        plt.ylabel('纬度', fontsize=12)
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/heatmaps/bike_flow_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_recommendations_map(self, recommendations):
        """绘制推荐位置地图"""
        print("Creating recommendations map...")
        
        plt.figure(figsize=(15, 12))
        
        # 散点图显示推荐位置
        scatter = plt.scatter(
            recommendations['grid_lon'],
            recommendations['grid_lat'],
            c=recommendations['business_score'],
            s=recommendations['business_score'] * 3,
            cmap='YlOrRd',
            alpha=0.7,
            edgecolors='black',
            linewidth=0.5
        )
        
        # 添加颜色条
        cbar = plt.colorbar(scatter)
        cbar.set_label('商业价值评分', fontsize=12)
        
        # 标注前5个推荐位置
        for i, row in recommendations.head(5).iterrows():
            plt.annotate(
                f'推荐{i+1}',
                (row['grid_lon'], row['grid_lat']),
                xytext=(5, 5),
                textcoords='offset points',
                fontsize=10,
                fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7)
            )
        
        plt.title('商业选址推荐位置图', fontsize=16, fontweight='bold')
        plt.xlabel('经度', fontsize=12)
        plt.ylabel('纬度', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/recommendations/recommendations_map.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_interactive_map(self, recommendations, grid_df):
        """创建交互式地图"""
        print("Creating interactive map...")
        
        # 计算地图中心
        center_lat = recommendations['grid_lat'].mean()
        center_lon = recommendations['grid_lon'].mean()
        
        # 创建地图
        m = folium.Map(
            location=[center_lat, center_lon],
            zoom_start=11,
            tiles='OpenStreetMap'
        )
        
        # 添加推荐位置标记
        for idx, row in recommendations.head(20).iterrows():
            folium.CircleMarker(
                location=[row['grid_lat'], row['grid_lon']],
                radius=row['business_score'] / 5,
                popup=f"""
                <b>推荐位置 {idx+1}</b><br>
                商业评分: {row['business_score']:.2f}<br>
                POI总数: {row['total_poi_count']:.0f}<br>
                商业POI: {row['business_poi_count']:.0f}<br>
                单车流量: {row['bike_flow_density']:.0f}
                """,
                color='red',
                fill=True,
                fillColor='red',
                fillOpacity=0.6
            ).add_to(m)
        
        # 添加热力图层（如果有单车流量数据）
        if 'bike_flow_density' in grid_df.columns:
            heat_data = []
            for _, row in grid_df.iterrows():
                if row['bike_flow_density'] > 0:
                    heat_data.append([
                        row['grid_lat'],
                        row['grid_lon'],
                        row['bike_flow_density']
                    ])
            
            if heat_data:
                HeatMap(heat_data, radius=15, blur=10, max_zoom=1).add_to(m)
        
        # 保存地图
        m.save(f'{self.results_dir}/recommendations/interactive_map.html')
        print(f"Interactive map saved to {self.results_dir}/recommendations/interactive_map.html")
        
        return m
    
    def plot_score_distribution(self, grid_df):
        """绘制评分分布图"""
        print("Creating score distribution plot...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 商业评分分布
        axes[0, 0].hist(grid_df['business_score'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('商业评分分布', fontweight='bold')
        axes[0, 0].set_xlabel('评分')
        axes[0, 0].set_ylabel('频次')
        
        # POI密度分布
        axes[0, 1].hist(grid_df['total_poi_count'], bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[0, 1].set_title('POI密度分布', fontweight='bold')
        axes[0, 1].set_xlabel('POI数量')
        axes[0, 1].set_ylabel('频次')
        
        # 单车流量分布
        if 'bike_flow_density' in grid_df.columns:
            axes[1, 0].hist(grid_df['bike_flow_density'], bins=50, alpha=0.7, color='orange', edgecolor='black')
            axes[1, 0].set_title('单车流量分布', fontweight='bold')
            axes[1, 0].set_xlabel('流量密度')
            axes[1, 0].set_ylabel('频次')
        
        # 评分与POI关系
        axes[1, 1].scatter(grid_df['total_poi_count'], grid_df['business_score'], alpha=0.5, color='purple')
        axes[1, 1].set_title('评分与POI数量关系', fontweight='bold')
        axes[1, 1].set_xlabel('POI数量')
        axes[1, 1].set_ylabel('商业评分')
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/analysis/score_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_summary_report(self, recommendations, grid_df):
        """生成汇总报告"""
        print("Generating summary report...")
        
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        
        # 1. 推荐位置评分
        top_10 = recommendations.head(10)
        bars = axes[0, 0].bar(range(len(top_10)), top_10['business_score'], color='gold', alpha=0.8)
        axes[0, 0].set_title('前10推荐位置评分', fontweight='bold')
        axes[0, 0].set_xlabel('推荐排名')
        axes[0, 0].set_ylabel('商业评分')
        axes[0, 0].set_xticks(range(len(top_10)))
        axes[0, 0].set_xticklabels([f'第{i+1}名' for i in range(len(top_10))], rotation=45)
        
        # 2. 各指标对比
        metrics = ['total_poi_count', 'business_poi_count', 'transport_poi_count']
        if 'bike_flow_density' in top_10.columns:
            metrics.append('bike_flow_density')
        
        metric_means = [top_10[metric].mean() for metric in metrics]
        metric_labels = ['总POI', '商业POI', '交通POI', '单车流量'][:len(metrics)]
        
        axes[0, 1].bar(metric_labels, metric_means, color=['skyblue', 'lightgreen', 'orange', 'pink'][:len(metrics)])
        axes[0, 1].set_title('推荐位置平均指标', fontweight='bold')
        axes[0, 1].set_ylabel('平均值')
        
        # 3. 评分分级统计
        score_ranges = ['0-20', '20-40', '40-60', '60-80', '80-100']
        score_counts = [
            len(grid_df[(grid_df['business_score'] >= i*20) & (grid_df['business_score'] < (i+1)*20)])
            for i in range(5)
        ]
        
        axes[0, 2].pie(score_counts, labels=score_ranges, autopct='%1.1f%%', startangle=90)
        axes[0, 2].set_title('评分分级分布', fontweight='bold')
        
        # 4. 地理分布
        scatter = axes[1, 0].scatter(
            recommendations['grid_lon'],
            recommendations['grid_lat'],
            c=recommendations['business_score'],
            s=50,
            cmap='YlOrRd',
            alpha=0.7
        )
        axes[1, 0].set_title('推荐位置地理分布', fontweight='bold')
        axes[1, 0].set_xlabel('经度')
        axes[1, 0].set_ylabel('纬度')
        
        # 5. 相关性分析
        if 'bike_flow_density' in grid_df.columns:
            axes[1, 1].scatter(grid_df['bike_flow_density'], grid_df['business_score'], alpha=0.5)
            axes[1, 1].set_title('单车流量与商业评分相关性', fontweight='bold')
            axes[1, 1].set_xlabel('单车流量密度')
            axes[1, 1].set_ylabel('商业评分')
        
        # 6. 统计摘要
        axes[1, 2].axis('off')
        summary_text = f"""
        分析摘要:
        
        • 总分析区域: {len(grid_df):,} 个网格
        • 推荐位置数: {len(recommendations)} 个
        • 最高评分: {recommendations['business_score'].max():.2f}
        • 平均评分: {recommendations['business_score'].mean():.2f}
        • 平均POI密度: {recommendations['total_poi_count'].mean():.1f}
        """
        
        if 'bike_flow_density' in recommendations.columns:
            summary_text += f"• 平均单车流量: {recommendations['bike_flow_density'].mean():.1f}"
        
        axes[1, 2].text(0.1, 0.5, summary_text, fontsize=12, verticalalignment='center',
                       bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(f'{self.results_dir}/summary_report.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """主函数"""
    # 加载数据
    try:
        grid_df = pd.read_csv('business_location_analysis.csv')
        recommendations = pd.read_csv('top_business_locations.csv')
        poi_data = pd.read_csv('北京市POI数据.csv')
        
        # 创建可视化
        visualizer = BusinessLocationVisualizer()
        
        # 生成各种图表
        visualizer.plot_business_score_heatmap(grid_df)
        visualizer.plot_poi_distribution(poi_data)
        visualizer.plot_bike_flow_heatmap(grid_df)
        visualizer.plot_recommendations_map(recommendations)
        visualizer.plot_score_distribution(grid_df)
        visualizer.generate_summary_report(recommendations, grid_df)
        
        # 创建交互式地图
        interactive_map = visualizer.create_interactive_map(recommendations, grid_df)
        
        print("All visualizations completed!")
        
    except FileNotFoundError as e:
        print(f"Required data file not found: {e}")
        print("Please run the analysis pipeline first.")

if __name__ == "__main__":
    main()
