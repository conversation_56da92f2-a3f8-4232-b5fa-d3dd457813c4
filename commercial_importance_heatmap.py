import pandas as pd
import numpy as np
import geopandas as gpd
import matplotlib.pyplot as plt
from shapely.geometry import Point
from matplotlib.colors import LinearSegmentedColormap
import os

# 尝试导入geohash相关库
try:
    # 尝试导入geohash2库
    import geohash2 as geohash
except ImportError:
    try:
        # 尝试导入geohash库
        import geohash
    except ImportError:
        
            # 尝试另一种导入方式
            from Geohash import geohash
        
    
class CommercialImportanceAnalyzer:
    def __init__(self):
        self.processed_data = None
        self.grid_gdf = None
        self.output_dir = 'results/heatmaps'
        os.makedirs(self.output_dir, exist_ok=True)

    def load_data(self, train_file_path, grid_shp_path):
        """加载训练数据和网格数据"""
        # 加载训练数据
        try:
            self.processed_data = pd.read_csv(train_file_path)
            print(f"成功加载训练数据: {train_file_path}")

            # 从starttime提取hour信息
            print("从starttime提取小时信息...")
            self.processed_data['starttime'] = pd.to_datetime(self.processed_data['starttime'])
            self.processed_data['hour'] = self.processed_data['starttime'].dt.hour
        except Exception as e:
            print(f"加载训练数据失败: {e}")
            return False

        # 加载网格数据
        try:
            self.grid_gdf = gpd.read_file(grid_shp_path)
            print(f"成功加载网格数据: {grid_shp_path}")
        except Exception as e:
            print(f"加载网格数据失败: {e}")
            return False

        # 确保网格数据有唯一标识符
        if 'grid_id' not in self.grid_gdf.columns:
            self.grid_gdf = self.grid_gdf.reset_index().rename(columns={self.grid_gdf.index.name or 'index': 'grid_id'})

        return True

    def calculate_commercial_importance(self):
        """计算每个网格的商业重要性指标"""
        if self.processed_data is None or self.grid_gdf is None:
            print("请先加载数据")
            return None

        # 1. 准备点数据用于空间连接
        # 将geohash转换为经纬度
        def geohash_to_lat_lng(geohash_str):
            try:
                lat, lng = geohash.decode(geohash_str)
                return lng, lat  # Point需要(lng, lat)格式
            except:
                return None, None

        # 应用转换
        self.processed_data[['start_lng', 'start_lat']] = \
            self.processed_data['geohashed_start_loc'].apply(\
                lambda x: pd.Series(geohash_to_lat_lng(x)))

        # 移除经纬度为None的行
        self.processed_data = self.processed_data.dropna(subset=['start_lng', 'start_lat'])

        # 创建点几何对象
        geometry = [Point(xy) for xy in zip(self.processed_data['start_lng'], self.processed_data['start_lat'])]
        points_gdf = gpd.GeoDataFrame(self.processed_data, geometry=geometry)
        points_gdf.crs = 'EPSG:4326'

        # 确保网格数据和点数据具有相同的CRS
        if points_gdf.crs != self.grid_gdf.crs:
            points_gdf = points_gdf.to_crs(self.grid_gdf.crs)
            print(f"已将点数据转换为网格数据的CRS: {self.grid_gdf.crs}")

        # 2. 空间连接：将点数据与网格匹配
        point_in_grid = gpd.sjoin(points_gdf, self.grid_gdf, how='left', predicate='within')
        print(f"空间连接完成，匹配到网格的点数: {len(point_in_grid[point_in_grid['grid_id'].notna()])}")

        # 3. 计算区域活跃度指数
        print("计算区域活跃度指数...")
        # 计算起点流量
        start_counts = self.processed_data['geohashed_start_loc'].value_counts().reset_index()
        start_counts.columns = ['location', 'start_count']

        # 计算终点流量
        end_counts = self.processed_data['geohashed_end_loc'].value_counts().reset_index()
        end_counts.columns = ['location', 'end_count']

        # 计算夜间流量 (22:00-6:00)
        night_data = self.processed_data[(self.processed_data['hour'] >= 22) | (self.processed_data['hour'] < 6)]
        night_counts = night_data['geohashed_start_loc'].value_counts().reset_index()
        night_counts.columns = ['location', 'night_count']

        # 合并数据
        area_metrics = start_counts.merge(end_counts, on='location', how='outer')
        area_metrics = area_metrics.merge(night_counts, on='location', how='outer')

        # 填充缺失值
        area_metrics = area_metrics.fillna(0)

        # 计算区域活跃度指数
        area_metrics['log_start_count'] = np.log1p(area_metrics['start_count'])
        area_metrics['start_score'] = area_metrics['log_start_count'] / area_metrics['log_start_count'].max()
        area_metrics['end_score'] = area_metrics['end_count'] / area_metrics['end_count'].max() if area_metrics['end_count'].max() > 0 else 0
        area_metrics['night_score'] = area_metrics['night_count'] / area_metrics['night_count'].max() if area_metrics['night_count'].max() > 0 else 0

        area_metrics['activity_index'] = (
            area_metrics['start_score'] * 0.4 + 
            area_metrics['end_score'] * 0.3 + 
            area_metrics['night_score'] * 0.3
        )

        # 归一化指数到0-100
        area_metrics['activity_index'] = (area_metrics['activity_index'] / area_metrics['activity_index'].max()) * 100

        # 4. 计算商业连接度指数
        print("计算商业连接度指数...")
        # 找到热门起点和终点
        top_n = 100
        popular_starts = self.processed_data['geohashed_start_loc'].value_counts().nlargest(top_n).index
        popular_ends = self.processed_data['geohashed_end_loc'].value_counts().nlargest(top_n).index

        # 筛选数据
        filtered_data = self.processed_data[
            self.processed_data['geohashed_start_loc'].isin(popular_starts) & 
            self.processed_data['geohashed_end_loc'].isin(popular_ends)
        ]

        # 检查筛选后的数据是否为空
        if filtered_data.empty:
            print(f"警告: 筛选后的数据为空，使用所有数据进行计算")
            filtered_data = self.processed_data

        # 构建OD矩阵
        od_matrix = filtered_data.groupby(['geohashed_start_loc', 'geohashed_end_loc']).size().unstack(fill_value=0)

        # 计算流入量和流出量
        inbound = od_matrix.sum(axis=0)  # 流入量
        outbound = od_matrix.sum(axis=1)  # 流出量

        # 检查是否有流量数据
        if inbound.empty and outbound.empty:
            print("警告: 没有流量数据，无法计算连接度")
            connectivity_df = None
        else:
            max_inbound = inbound.max() if not inbound.empty else 0
            max_outbound = outbound.max() if not outbound.empty else 0

            # 避免分母为0
            if max_inbound + max_outbound == 0:
                print("警告: 总流量为0，无法计算连接度")
                connectivity_df = None
            else:
                # 计算连接度
                connectivity = pd.DataFrame()
                connectivity['location'] = list(set(inbound.index) | set(outbound.index))
                connectivity['inbound'] = connectivity['location'].map(inbound).fillna(0)
                connectivity['outbound'] = connectivity['location'].map(outbound).fillna(0)
                connectivity['connectivity_score'] = (
                    (connectivity['inbound'] + connectivity['outbound']) / 
                    (max_inbound + max_outbound)
                )
                connectivity_df = connectivity

        # 5. 合并活跃度指数和连接度指数到网格
        # 按网格ID聚合活跃度指数
        grid_activity = point_in_grid.merge(area_metrics, left_on='geohashed_start_loc', right_on='location', how='left')
        grid_activity = grid_activity.groupby('grid_id')['activity_index'].mean().reset_index()

        # 合并活跃度指数到网格数据
        grid_gdf = self.grid_gdf.merge(grid_activity, on='grid_id', how='left')
        grid_gdf['activity_index'] = grid_gdf['activity_index'].fillna(0)

        # 如果连接度计算成功，合并连接度指数
        if connectivity_df is not None:
            # 按网格ID聚合连接度指数
            grid_connectivity = point_in_grid.merge(connectivity_df, left_on='geohashed_start_loc', right_on='location', how='left')
            grid_connectivity = grid_connectivity.groupby('grid_id')['connectivity_score'].mean().reset_index()

            # 合并到网格数据
            grid_gdf = grid_gdf.merge(grid_connectivity, on='grid_id', how='left')
            grid_gdf['connectivity_score'] = grid_gdf['connectivity_score'].fillna(0)

            # 计算综合得分作为商业重要性指标
            grid_gdf['commercial_importance'] = (
                grid_gdf['activity_index'] * 0.6 + 
                grid_gdf['connectivity_score'] * 100 * 0.4  # 连接度归一化到0-100
            )
        else:
            # 如果连接度计算失败，仅使用活跃度指数
            grid_gdf['commercial_importance'] = grid_gdf['activity_index']

        print("商业重要性指标计算完成")
        return grid_gdf

    def generate_commercial_heatmap(self, grid_gdf, output_path=None):
        """生成商业重要性热力图"""
        if grid_gdf is None:
            print("没有可用的网格数据")
            return False

        # 创建高对比度的自定义颜色映射
        colors = ['#FFFFFF', '#FFEE00', '#FF9900', '#FF0000', '#CC0066', '#6600CC', '#000099']
        cmap = LinearSegmentedColormap.from_list('custom_cmap', colors, N=256)

        # 使用数据的95%分位数作为最大值，增强对比度
        vmax = grid_gdf['commercial_importance'].quantile(0.95)

        # 绘制热力图
        fig, ax = plt.subplots(1, 1, figsize=(12, 10))

        # 设置中文字体
        plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]

        # 绘制网格，应用vmax增强对比度
        grid_gdf.plot(column='commercial_importance', cmap=cmap, linewidth=0.8, ax=ax, edgecolor='0.8', alpha=0.8, vmax=vmax)

        # 添加颜色条
        cbar = plt.colorbar(ax.collections[0], ax=ax, orientation='vertical', fraction=0.03, pad=0.05)
        cbar.set_label('商业重要性指数', rotation=270, labelpad=20)

        # 设置标题和坐标轴
        plt.title('北京市商业重要性热力图', fontsize=15)
        plt.xlabel('经度', fontsize=12)
        plt.ylabel('纬度', fontsize=12)

        # 保存或显示热力图
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            print(f"热力图已保存至: {output_path}")
        else:
            plt.show()

        return True

if __name__ == '__main__':
    # 创建分析器实例
    analyzer = CommercialImportanceAnalyzer()

    # 加载数据
    if analyzer.load_data('train.csv', 'D:\\桌面\\比赛文件\\商业选址\\SAU\\SAU\\Grid\\BeijingGrid_1km_3857.shp'):
        # 计算商业重要性指标
        grid_gdf = analyzer.calculate_commercial_importance()

        # 生成热力图
        analyzer.generate_commercial_heatmap(
            grid_gdf, 
            output_path=os.path.join(analyzer.output_dir, 'commercial_importance_heatmap.png')
        )