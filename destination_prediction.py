import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler, LabelEncoder
import joblib
import warnings
warnings.filterwarnings('ignore')

class DestinationPredictor:
    """摩拜单车终点预测模型"""
    
    def __init__(self):
        self.lat_model = None
        self.lon_model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_columns = []
        
    def prepare_features(self, df):
        """准备特征数据"""
        features = df.copy()
        
        # 基础特征
        feature_cols = [
            'userid', 'bikeid', 'biketype', 'hour', 'day_of_week', 
            'is_weekend', 'is_rush_hour', 'geohashed_start_loc_lat', 
            'geohashed_start_loc_lon'
        ]
        
        # 用户行为特征
        if 'userid' in features.columns:
            user_stats = features.groupby('userid').agg({
                'geohashed_start_loc_lat': ['mean', 'std'],
                'geohashed_start_loc_lon': ['mean', 'std'],
                'hour': 'mean'
            }).fillna(0)
            
            user_stats.columns = ['user_lat_mean', 'user_lat_std', 'user_lon_mean', 'user_lon_std', 'user_hour_mean']
            features = features.merge(user_stats, left_on='userid', right_index=True, how='left')
            
            feature_cols.extend(['user_lat_mean', 'user_lat_std', 'user_lon_mean', 'user_lon_std', 'user_hour_mean'])
        
        # 位置聚类特征
        features['start_lat_rounded'] = features['geohashed_start_loc_lat'].round(3)
        features['start_lon_rounded'] = features['geohashed_start_loc_lon'].round(3)
        
        # 编码分类特征
        categorical_cols = ['biketype']
        for col in categorical_cols:
            if col in features.columns:
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    features[f'{col}_encoded'] = self.label_encoders[col].fit_transform(features[col].astype(str))
                else:
                    features[f'{col}_encoded'] = self.label_encoders[col].transform(features[col].astype(str))
                feature_cols.append(f'{col}_encoded')
        
        # 时间周期特征
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        features['dow_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
        features['dow_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
        
        feature_cols.extend(['hour_sin', 'hour_cos', 'dow_sin', 'dow_cos'])
        
        # 选择最终特征
        available_cols = [col for col in feature_cols if col in features.columns]
        self.feature_columns = available_cols
        
        return features[available_cols].fillna(0)
    
    def train_model(self, train_data):
        """训练预测模型"""
        print("Preparing training features...")
        X = self.prepare_features(train_data)
        y_lat = train_data['geohashed_end_loc_lat']
        y_lon = train_data['geohashed_end_loc_lon']
        
        print(f"Feature shape: {X.shape}")
        print(f"Features: {self.feature_columns}")
        
        # 分割训练和验证集
        X_train, X_val, y_lat_train, y_lat_val, y_lon_train, y_lon_val = train_test_split(
            X, y_lat, y_lon, test_size=0.2, random_state=42
        )
        
        # 标准化特征
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        print("Training latitude model...")
        self.lat_model = GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42,
            subsample=0.8
        )
        self.lat_model.fit(X_train_scaled, y_lat_train)
        
        print("Training longitude model...")
        self.lon_model = GradientBoostingRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42,
            subsample=0.8
        )
        self.lon_model.fit(X_train_scaled, y_lon_train)
        
        # 验证模型
        lat_pred = self.lat_model.predict(X_val_scaled)
        lon_pred = self.lon_model.predict(X_val_scaled)
        
        lat_mse = mean_squared_error(y_lat_val, lat_pred)
        lon_mse = mean_squared_error(y_lon_val, lon_pred)
        lat_mae = mean_absolute_error(y_lat_val, lat_pred)
        lon_mae = mean_absolute_error(y_lon_val, lon_pred)
        
        print(f"Validation Results:")
        print(f"Latitude - MSE: {lat_mse:.6f}, MAE: {lat_mae:.6f}")
        print(f"Longitude - MSE: {lon_mse:.6f}, MAE: {lon_mae:.6f}")
        
        # 特征重要性
        lat_importance = pd.DataFrame({
            'feature': self.feature_columns,
            'importance': self.lat_model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\nTop 10 features for latitude prediction:")
        print(lat_importance.head(10))
        
        return {
            'lat_mse': lat_mse,
            'lon_mse': lon_mse,
            'lat_mae': lat_mae,
            'lon_mae': lon_mae
        }
    
    def predict(self, test_data):
        """预测终点位置"""
        print("Preparing test features...")
        X_test = self.prepare_features(test_data)
        X_test_scaled = self.scaler.transform(X_test)
        
        print("Predicting destinations...")
        lat_pred = self.lat_model.predict(X_test_scaled)
        lon_pred = self.lon_model.predict(X_test_scaled)
        
        # 创建预测结果
        predictions = test_data[['orderid']].copy()
        predictions['predicted_lat'] = lat_pred
        predictions['predicted_lon'] = lon_pred
        
        return predictions
    
    def save_model(self, model_path='destination_model.pkl'):
        """保存模型"""
        model_data = {
            'lat_model': self.lat_model,
            'lon_model': self.lon_model,
            'scaler': self.scaler,
            'label_encoders': self.label_encoders,
            'feature_columns': self.feature_columns
        }
        joblib.dump(model_data, model_path)
        print(f"Model saved to {model_path}")
    
    def load_model(self, model_path='destination_model.pkl'):
        """加载模型"""
        model_data = joblib.load(model_path)
        self.lat_model = model_data['lat_model']
        self.lon_model = model_data['lon_model']
        self.scaler = model_data['scaler']
        self.label_encoders = model_data['label_encoders']
        self.feature_columns = model_data['feature_columns']
        print(f"Model loaded from {model_path}")

def main():
    """主函数"""
    # 加载处理后的数据
    print("Loading processed data...")
    train_data = pd.read_csv('processed_train.csv')
    test_data = pd.read_csv('processed_test.csv')
    
    # 采样训练数据（如果数据太大）
    if len(train_data) > 500000:
        print("Sampling training data...")
        train_data = train_data.sample(n=500000, random_state=42)
    
    # 训练模型
    predictor = DestinationPredictor()
    metrics = predictor.train_model(train_data)
    
    # 保存模型
    predictor.save_model()
    
    # 预测测试集
    predictions = predictor.predict(test_data)
    
    # 保存预测结果
    predictions.to_csv('destination_predictions.csv', index=False)
    print("Predictions saved to destination_predictions.csv")
    
    return predictions

if __name__ == "__main__":
    predictions = main()
