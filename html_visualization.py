"""
摩拜单车商业选址推荐系统 - HTML格式可视化
生成HTML格式的可视化图表，完美支持中文显示
"""

import pandas as pd
import numpy as np
import geopandas as gpd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
import folium
from folium.plugins import HeatMap, MarkerCluster, Fullscreen
import warnings
warnings.filterwarnings('ignore')

class HTMLVisualization:
    """HTML格式可视化类"""
    
    def __init__(self):
        self.results_dir = 'results/html_viz'
        self.grid_gdf = None
        self.poi_data = None
        self.ensure_results_dir()
        
        # 专业配色方案
        self.colors = {
            'business': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'],
            'heatmap_business': px.colors.sequential.Reds,
            'heatmap_flow': px.colors.sequential.Blues,
            'heatmap_potential': px.colors.sequential.Greens,
            'comprehensive': px.colors.sequential.Viridis
        }
    
    def ensure_results_dir(self):
        """确保结果目录存在"""
        import os
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
    
    def load_all_data(self):
        """加载所有必要数据"""
        print("Loading all data for HTML visualization...")
        
        try:
            # 加载网格分析结果
            grid_shp_path = 'results/grid_analysis/grid_analysis_results.shp'
            self.grid_gdf = gpd.read_file(grid_shp_path)
            
            # 修复列名
            column_mapping = {
                'total_poi_': 'total_poi_count',
                'business_p': 'business_poi_count',
                'transport_': 'transport_poi_count',
                'education_': 'education_poi_count',
                'medical_po': 'medical_poi_count',
                'bike_flow_': 'bike_flow_density',
                'weighted_f': 'weighted_flow_density',
                'start_dens': 'start_density',
                'end_densit': 'end_density',
                'predicted_': 'predicted_density',
                'test_start': 'test_start_density',
                'business_s': 'business_score',
                'future_pot': 'future_potential_score',
                'comprehens': 'comprehensive_score'
            }
            self.grid_gdf = self.grid_gdf.rename(columns=column_mapping)
            
            print(f"Grid data loaded: {len(self.grid_gdf)} cells")
            
            # 加载POI数据
            self.poi_data = pd.read_csv('北京市POI数据.csv')
            print(f"POI data loaded: {len(self.poi_data)} records")
            
            return True
            
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
    
    def create_master_heatmap_dashboard_html(self):
        """创建主热力图仪表板HTML版本"""
        print("Creating master heatmap dashboard in HTML format...")
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=[
                '🎯 综合评分热力图',
                '💼 商业价值热力图', 
                '🚀 未来潜力热力图',
                '🚲 加权流量密度热力图'
            ],
            specs=[[{"type": "scatter"}, {"type": "scatter"}],
                   [{"type": "scatter"}, {"type": "scatter"}]]
        )
        
        # 1. 综合评分热力图
        fig.add_trace(
            go.Scatter(
                x=self.grid_gdf['center_lon'],
                y=self.grid_gdf['center_lat'],
                mode='markers',
                marker=dict(
                    size=8,
                    color=self.grid_gdf['comprehensive_score'],
                    colorscale='Viridis',
                    showscale=True,
                    colorbar=dict(title="综合评分", x=0.48, len=0.4, y=0.8)
                ),
                text=[f'网格{gid}<br>综合评分: {score:.1f}' 
                      for gid, score in zip(self.grid_gdf['grid_id'], self.grid_gdf['comprehensive_score'])],
                hovertemplate='%{text}<extra></extra>',
                name='综合评分'
            ),
            row=1, col=1
        )
        
        # 2. 商业价值热力图
        fig.add_trace(
            go.Scatter(
                x=self.grid_gdf['center_lon'],
                y=self.grid_gdf['center_lat'],
                mode='markers',
                marker=dict(
                    size=8,
                    color=self.grid_gdf['business_score'],
                    colorscale='Reds',
                    showscale=True,
                    colorbar=dict(title="商业价值", x=1.02, len=0.4, y=0.8)
                ),
                text=[f'网格{gid}<br>商业价值: {score:.1f}' 
                      for gid, score in zip(self.grid_gdf['grid_id'], self.grid_gdf['business_score'])],
                hovertemplate='%{text}<extra></extra>',
                name='商业价值'
            ),
            row=1, col=2
        )
        
        # 3. 未来潜力热力图
        fig.add_trace(
            go.Scatter(
                x=self.grid_gdf['center_lon'],
                y=self.grid_gdf['center_lat'],
                mode='markers',
                marker=dict(
                    size=8,
                    color=self.grid_gdf['future_potential_score'],
                    colorscale='Greens',
                    showscale=True,
                    colorbar=dict(title="未来潜力", x=0.48, len=0.4, y=0.2)
                ),
                text=[f'网格{gid}<br>未来潜力: {score:.1f}' 
                      for gid, score in zip(self.grid_gdf['grid_id'], self.grid_gdf['future_potential_score'])],
                hovertemplate='%{text}<extra></extra>',
                name='未来潜力'
            ),
            row=2, col=1
        )
        
        # 4. 加权流量密度热力图
        fig.add_trace(
            go.Scatter(
                x=self.grid_gdf['center_lon'],
                y=self.grid_gdf['center_lat'],
                mode='markers',
                marker=dict(
                    size=8,
                    color=self.grid_gdf['weighted_flow_density'],
                    colorscale='Blues',
                    showscale=True,
                    colorbar=dict(title="流量密度", x=1.02, len=0.4, y=0.2)
                ),
                text=[f'网格{gid}<br>流量密度: {density:.0f}' 
                      for gid, density in zip(self.grid_gdf['grid_id'], self.grid_gdf['weighted_flow_density'])],
                hovertemplate='%{text}<extra></extra>',
                name='流量密度'
            ),
            row=2, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title={
                'text': '🎯 摩拜单车商业选址热力图仪表板',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 24, 'family': 'Arial, sans-serif'}
            },
            height=800,
            showlegend=False,
            template='plotly_white'
        )
        
        # 更新坐标轴
        for i in range(1, 3):
            for j in range(1, 3):
                fig.update_xaxes(title_text="经度", row=i, col=j)
                fig.update_yaxes(title_text="纬度", row=i, col=j)
        
        # 保存HTML文件
        fig.write_html(f'{self.results_dir}/master_heatmap_dashboard.html')
        print(f"Master heatmap dashboard saved to {self.results_dir}/master_heatmap_dashboard.html")
        
        return fig
    
    def create_business_analysis_dashboard_html(self):
        """创建商业分析仪表板HTML版本"""
        print("Creating business analysis dashboard in HTML format...")
        
        # 获取推荐数据
        top_20 = self.grid_gdf.nlargest(20, 'comprehensive_score')
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=3,
            subplot_titles=[
                '📊 前10推荐位置综合评分',
                '🏢 北京市POI分布',
                '🔗 评分相关性矩阵',
                '📍 推荐位置地理分布',
                '🚲 平均流量密度分析',
                '📦 评分分布箱线图'
            ],
            specs=[
                [{"type": "bar"}, {"type": "pie"}, {"type": "heatmap"}],
                [{"type": "scatter"}, {"type": "bar"}, {"type": "box"}]
            ]
        )
        
        # 1. 推荐位置评分对比
        top_10 = top_20.head(10)
        fig.add_trace(
            go.Bar(
                x=[f'第{i+1}名' for i in range(len(top_10))],
                y=top_10['comprehensive_score'],
                marker_color=px.colors.qualitative.Set1,
                text=[f'{score:.1f}' for score in top_10['comprehensive_score']],
                textposition='auto',
                name='综合评分'
            ),
            row=1, col=1
        )
        
        # 2. POI分布统计
        poi_counts = self.poi_data['大类'].value_counts().head(8)
        fig.add_trace(
            go.Pie(
                labels=poi_counts.index,
                values=poi_counts.values,
                name="POI分布"
            ),
            row=1, col=2
        )
        
        # 3. 评分相关性分析
        score_data = top_20[['business_score', 'future_potential_score', 'comprehensive_score']]
        correlation = score_data.corr()
        fig.add_trace(
            go.Heatmap(
                z=correlation.values,
                x=['商业价值', '未来潜力', '综合评分'],
                y=['商业价值', '未来潜力', '综合评分'],
                colorscale='RdBu',
                zmid=0,
                text=correlation.values,
                texttemplate='%{text:.2f}',
                textfont={"size": 12},
                name='相关性'
            ),
            row=1, col=3
        )
        
        # 4. 推荐位置地理分布
        fig.add_trace(
            go.Scatter(
                x=top_20['center_lon'],
                y=top_20['center_lat'],
                mode='markers+text',
                marker=dict(
                    size=top_20['comprehensive_score']/3,
                    color=top_20['comprehensive_score'],
                    colorscale='Viridis',
                    showscale=True,
                    colorbar=dict(title="综合评分", x=0.48)
                ),
                text=[f'#{i+1}' for i in range(len(top_20))],
                textposition="middle center",
                hovertemplate='推荐位置 #%{text}<br>经度: %{x}<br>纬度: %{y}<br>评分: %{marker.color:.1f}<extra></extra>',
                name='推荐位置'
            ),
            row=2, col=1
        )
        
        # 5. 流量类型分析
        flow_types = ['start_density', 'end_density', 'predicted_density', 'test_start_density']
        flow_labels = ['起点密度', '终点密度', '预测密度', '测试起点密度']
        flow_means = [top_20[col].mean() for col in flow_types]
        
        fig.add_trace(
            go.Bar(
                x=flow_labels,
                y=flow_means,
                marker_color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
                text=[f'{val:.0f}' for val in flow_means],
                textposition='auto',
                name='流量分析'
            ),
            row=2, col=2
        )
        
        # 6. 评分分布箱线图
        for i, (col, name) in enumerate([('business_score', '商业价值'), 
                                        ('future_potential_score', '未来潜力'), 
                                        ('comprehensive_score', '综合评分')]):
            fig.add_trace(
                go.Box(
                    y=top_20[col],
                    name=name,
                    boxpoints='outliers'
                ),
                row=2, col=3
            )
        
        # 更新布局
        fig.update_layout(
            title={
                'text': '📈 商业选址分析仪表板',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 24, 'family': 'Arial, sans-serif'}
            },
            height=1000,
            showlegend=True,
            template='plotly_white'
        )
        
        # 保存HTML文件
        fig.write_html(f'{self.results_dir}/business_analysis_dashboard.html')
        print(f"Business analysis dashboard saved to {self.results_dir}/business_analysis_dashboard.html")
        
        return fig
    
    def create_simple_summary_chart_html(self):
        """创建简化总结图表HTML版本"""
        print("Creating simple summary chart in HTML format...")
        
        top_10 = self.grid_gdf.nlargest(10, 'comprehensive_score')
        
        # 创建子图
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=['📊 前10推荐位置综合评分', '📈 推荐位置平均指标'],
            specs=[[{"type": "bar"}, {"type": "bar"}]]
        )
        
        # 1. 推荐位置评分
        fig.add_trace(
            go.Bar(
                x=[f'第{i+1}名' for i in range(len(top_10))],
                y=top_10['comprehensive_score'],
                marker_color='skyblue',
                marker_line_color='navy',
                marker_line_width=2,
                text=[f'{score:.1f}' for score in top_10['comprehensive_score']],
                textposition='auto',
                name='综合评分'
            ),
            row=1, col=1
        )
        
        # 2. 关键指标对比
        metrics = ['商业价值', '未来潜力', 'POI密度', '流量密度']
        values = [
            top_10['business_score'].mean(),
            top_10['future_potential_score'].mean(),
            top_10['total_poi_count'].mean() / 20,  # 缩放到合适范围
            top_10['weighted_flow_density'].mean() / 50  # 缩放到合适范围
        ]
        
        fig.add_trace(
            go.Bar(
                x=metrics,
                y=values,
                marker_color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
                marker_line_color='black',
                marker_line_width=2,
                text=[f'{val:.1f}' for val in values],
                textposition='auto',
                name='平均指标'
            ),
            row=1, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title={
                'text': '🎯 摩拜单车商业选址推荐总结',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 20, 'family': 'Arial, sans-serif'}
            },
            height=500,
            showlegend=False,
            template='plotly_white'
        )
        
        # 更新坐标轴
        fig.update_xaxes(title_text="推荐排名", row=1, col=1)
        fig.update_yaxes(title_text="综合评分", row=1, col=1)
        fig.update_xaxes(title_text="指标类型", row=1, col=2)
        fig.update_yaxes(title_text="标准化数值", row=1, col=2)
        
        # 保存HTML文件
        fig.write_html(f'{self.results_dir}/simple_summary_chart.html')
        print(f"Simple summary chart saved to {self.results_dir}/simple_summary_chart.html")
        
        return fig
    
    def create_comprehensive_dashboard_html(self):
        """创建综合仪表板HTML版本"""
        print("Creating comprehensive dashboard in HTML format...")
        
        top_20 = self.grid_gdf.nlargest(20, 'comprehensive_score')
        
        # 创建子图
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=[
                '🎯 综合评分分布', '📍 推荐位置地理分布',
                '💼 商业价值 vs 🚀 未来潜力', '🚲 流量密度分析',
                '🕸️ 评分对比雷达图', '🏢 POI密度统计'
            ],
            specs=[
                [{"type": "histogram"}, {"type": "scatter"}],
                [{"type": "scatter"}, {"type": "bar"}],
                [{"type": "scatterpolar"}, {"type": "box"}]
            ]
        )
        
        # 1. 综合评分分布
        fig.add_trace(
            go.Histogram(
                x=self.grid_gdf['comprehensive_score'],
                nbinsx=50,
                name='评分分布',
                marker_color='skyblue',
                opacity=0.7
            ),
            row=1, col=1
        )
        
        # 2. 推荐位置地理分布
        fig.add_trace(
            go.Scatter(
                x=top_20['center_lon'],
                y=top_20['center_lat'],
                mode='markers+text',
                marker=dict(
                    size=top_20['comprehensive_score']/3,
                    color=top_20['comprehensive_score'],
                    colorscale='Viridis',
                    showscale=True,
                    colorbar=dict(title="综合评分", x=0.48)
                ),
                text=[f'#{i+1}' for i in range(len(top_20))],
                textposition="middle center",
                name='推荐位置',
                hovertemplate='网格%{text}<br>经度: %{x}<br>纬度: %{y}<br>评分: %{marker.color:.1f}<extra></extra>'
            ),
            row=1, col=2
        )
        
        # 3. 商业价值 vs 未来潜力
        fig.add_trace(
            go.Scatter(
                x=top_20['business_score'],
                y=top_20['future_potential_score'],
                mode='markers',
                marker=dict(
                    size=top_20['comprehensive_score']/4,
                    color=top_20['comprehensive_score'],
                    colorscale='RdYlBu_r',
                    line=dict(width=2, color='white')
                ),
                text=[f'网格{gid}' for gid in top_20['grid_id']],
                name='评分关系',
                hovertemplate='%{text}<br>商业价值: %{x:.1f}<br>未来潜力: %{y:.1f}<extra></extra>'
            ),
            row=2, col=1
        )
        
        # 4. 流量密度分析
        flow_types = ['start_density', 'end_density', 'predicted_density', 'test_start_density']
        flow_labels = ['起点密度', '终点密度', '预测密度', '测试起点密度']
        flow_means = [top_20[col].mean() for col in flow_types]
        
        fig.add_trace(
            go.Bar(
                x=flow_labels,
                y=flow_means,
                marker_color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
                name='流量分析',
                text=[f'{val:.0f}' for val in flow_means],
                textposition='auto'
            ),
            row=2, col=2
        )
        
        # 5. 评分对比雷达图
        top_5 = top_20.head(5)
        for i, (_, row) in enumerate(top_5.iterrows()):
            fig.add_trace(
                go.Scatterpolar(
                    r=[row['business_score'], row['future_potential_score'], 
                       row['total_poi_count']/20, row['weighted_flow_density']/50],
                    theta=['商业价值', '未来潜力', 'POI密度', '流量密度'],
                    fill='toself',
                    name=f'网格{row["grid_id"]}',
                    opacity=0.6
                ),
                row=3, col=1
            )
        
        # 6. POI密度统计
        poi_categories = ['total_poi_count', 'business_poi_count', 'transport_poi_count']
        poi_labels = ['总POI', '商业POI', '交通POI']
        
        for cat, label in zip(poi_categories, poi_labels):
            fig.add_trace(
                go.Box(
                    y=top_20[cat],
                    name=label,
                    boxpoints='outliers'
                ),
                row=3, col=2
            )
        
        # 更新布局
        fig.update_layout(
            title={
                'text': '🎯 摩拜单车商业选址综合分析仪表板',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 24, 'family': 'Arial, sans-serif'}
            },
            height=1200,
            showlegend=True,
            template='plotly_white'
        )
        
        # 保存HTML文件
        fig.write_html(f'{self.results_dir}/comprehensive_dashboard.html')
        print(f"Comprehensive dashboard saved to {self.results_dir}/comprehensive_dashboard.html")
        
        return fig

def main():
    """主函数"""
    print("🚀 Starting HTML Visualization System...")
    
    # 创建HTML可视化对象
    viz = HTMLVisualization()
    
    # 加载所有数据
    if not viz.load_all_data():
        print("❌ Failed to load data")
        return
    
    print("✅ Data loaded successfully!")
    
    # 创建所有HTML可视化
    print("\n📊 Creating HTML visualizations...")
    
    # 1. 主热力图仪表板
    viz.create_master_heatmap_dashboard_html()
    
    # 2. 商业分析仪表板
    viz.create_business_analysis_dashboard_html()
    
    # 3. 简化总结图表
    viz.create_simple_summary_chart_html()
    
    # 4. 综合仪表板
    viz.create_comprehensive_dashboard_html()
    
    print(f"\n🎉 All HTML visualizations completed!")
    print(f"📁 Results saved in: {viz.results_dir}")
    print(f"🌐 Open the HTML files in your browser to view the interactive charts!")

if __name__ == "__main__":
    main()
