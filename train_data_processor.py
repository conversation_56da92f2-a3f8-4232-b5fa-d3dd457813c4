import pandas as pd
import geopandas as gpd
from shapely.geometry import Point, Polygon
import numpy as np
import os
import matplotlib.pyplot as plt

from matplotlib.colors import LinearSegmentedColormap
import argparse
import geohash2

# 设置中文字体
plt.rcParams['font.family'] = ['SimHei', 'WenQuanYi Micro Hei', 'Heiti TC']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class TrainDataProcessor:
    """处理train.csv数据并转换为商业选址指标"""
    def __init__(self, file_path="train.csv"):
        self.file_path = file_path
        self.data = None
        self.processed_data = None
        
    def load_data(self):
        """加载train.csv数据"""
        try:
            self.data = pd.read_csv(self.file_path)
            print(f"成功加载数据，共{len(self.data)}条记录")
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
            
    def preprocess_data(self, chunk_size=100000):
        """预处理数据，包括时间格式转换和地理编码解析
        
        参数:
        chunk_size: int, 分块处理的大小，用于优化大数据集处理
        """
        if self.data is None:
            print("请先加载数据")
            return False
            
        total_rows = len(self.data)
        print(f"开始预处理{total_rows}条数据...")
        
        # 转换时间格式
        print("转换时间格式...")
        self.data['starttime'] = pd.to_datetime(self.data['starttime'])
        print("时间格式转换完成")
        
        # 提取小时、日期、星期等时间特征
        print("提取时间特征...")
        self.data['hour'] = self.data['starttime'].dt.hour
        self.data['day'] = self.data['starttime'].dt.day
        self.data['weekday'] = self.data['starttime'].dt.weekday
        
        # 标记工作日/周末
        self.data['is_weekend'] = self.data['weekday'].apply(lambda x: 1 if x >= 5 else 0)
        print("时间特征提取完成")
        
        # 解析geohash位置
        try:
            # 安装geohash2库: pip install geohash2
            import geohash2
            
            def decode_geohash(geohash):
                try:
                    lat, lng = geohash2.decode(geohash)
                    return lng, lat  # 返回经度和纬度
                except Exception as e:
                    print(f"geohash解码错误: {e}")
                    return 0, 0
            
            # 解码起点位置 - 分块处理以提高效率
            print("开始解码起点位置...")
            start_lng_list = []
            start_lat_list = []
            
            # 计算需要的块数
            num_chunks = (total_rows + chunk_size - 1) // chunk_size
            
            for i in range(num_chunks):
                start_idx = i * chunk_size
                end_idx = min((i + 1) * chunk_size, total_rows)
                
                # 处理当前块
                chunk = self.data.iloc[start_idx:end_idx]
                decoded = chunk['geohashed_start_loc'].apply(decode_geohash)
                
                # 提取经度和纬度
                lngs, lats = zip(*decoded)
                start_lng_list.extend(lngs)
                start_lat_list.extend(lats)
                
                # 打印进度
                print(f"解码起点位置: {end_idx}/{total_rows} ({end_idx/total_rows*100:.1f}%)")
            
            # 赋值结果
            self.data['start_lng'] = start_lng_list
            self.data['start_lat'] = start_lat_list
            print("起点位置解码完成")
            
            # 解码终点位置 - 分块处理以提高效率
            print("开始解码终点位置...")
            end_lng_list = []
            end_lat_list = []
            
            for i in range(num_chunks):
                start_idx = i * chunk_size
                end_idx = min((i + 1) * chunk_size, total_rows)
                
                # 处理当前块
                chunk = self.data.iloc[start_idx:end_idx]
                decoded = chunk['geohashed_end_loc'].apply(decode_geohash)
                
                # 提取经度和纬度
                lngs, lats = zip(*decoded)
                end_lng_list.extend(lngs)
                end_lat_list.extend(lats)
                
                # 打印进度
                print(f"解码终点位置: {end_idx}/{total_rows} ({end_idx/total_rows*100:.1f}%)")
            
            # 赋值结果
            self.data['end_lng'] = end_lng_list
            self.data['end_lat'] = end_lat_list
            print("终点位置解码完成")
        except ImportError:
            print("未找到geohash2库，使用随机位置数据")
            # 生成随机位置数据用于演示
            # np.random.seed(42)
            # self.data['start_lng'] = np.random.uniform(116.0, 117.0, len(self.data))
            # self.data['start_lat'] = np.random.uniform(39.5, 40.5, len(self.data))
            # self.data['end_lng'] = np.random.uniform(116.0, 117.0, len(self.data))
            # self.data['end_lat'] = np.random.uniform(39.5, 40.5, len(self.data))
        except Exception as e:
            print(f"geohash解码过程中出错: {e}")
            return False
        
        self.processed_data = self.data
        print("数据预处理完成")
        return True
        
    def generate_location_density(self, grid_size=0.01):
        """生成位置密度指标"""
        if self.processed_data is None:
            print("请先预处理数据")
            return None
            
        # 创建起点位置的GeoDataFrame
        geometry = [Point(xy) for xy in zip(self.processed_data['start_lng'], self.processed_data['start_lat'])]
        gdf = gpd.GeoDataFrame(self.processed_data, geometry=geometry)
        
        # 计算网格密度
        x_min, y_min, x_max, y_max = gdf.total_bounds
        x_grid = np.arange(x_min, x_max, grid_size)
        y_grid = np.arange(y_min, y_max, grid_size)
        
        # 创建网格并计算每个网格内的起点数量
        grid_geoms = []
        grid_data = []
        
        for i in range(len(x_grid) - 1):
            for j in range(len(y_grid) - 1):
                #计算网格中心
                grid_geom = Point(x_grid[i] + grid_size/2, y_grid[j] + grid_size/2)
                
                count = len(gdf.cx[x_grid[i]:x_grid[i+1], y_grid[j]:y_grid[j+1]])
                
                grid_geoms.append(grid_geom)
                grid_data.append({
                    'density': count,
                    'grid_x': i,
                    'grid_y': j
                })
        
        # 创建GeoDataFrame
        density_gdf = gpd.GeoDataFrame(grid_data, geometry=grid_geoms)
        return density_gdf
        
    def filter_time_period(self, period_type):
        """根据时间段类型筛选数据
        period_type: 'weekday' (工作日), 'weekend' (周末), 'morning_peak' (早高峰), 'evening_peak' (晚高峰)
        """
        if self.processed_data is None:
            print("请先预处理数据")
            return None

        filtered_data = self.processed_data.copy()

        if period_type == 'weekday':
            filtered_data = filtered_data[filtered_data['is_weekend'] == 0]
            print("筛选工作日数据完成")
        elif period_type == 'weekend':
            filtered_data = filtered_data[filtered_data['is_weekend'] == 1]
            print("筛选周末数据完成")
        elif period_type == 'morning_peak':
            # 早高峰定义为7:00-9:00
            filtered_data = filtered_data[(filtered_data['hour'] >= 7) & (filtered_data['hour'] < 10)]
            print("筛选早高峰数据完成")
        elif period_type == 'evening_peak':
            # 晚高峰定义为17:00-19:00
            filtered_data = filtered_data[(filtered_data['hour'] >= 17) & (filtered_data['hour'] < 20)]
            print("筛选晚高峰数据完成")
        else:
            print("无效的时间段类型")
            return None

        return filtered_data

    def generate_location_density(self, grid_size=0.01, filtered_data=None):
        """生成位置密度指标
        filtered_data: 可选，筛选后的数据
        """
        if filtered_data is None:
            if self.processed_data is None:
                print("请先预处理数据")
                return None
            filtered_data = self.processed_data

        # 创建起点位置的GeoDataFrame
        geometry = [Point(xy) for xy in zip(filtered_data['start_lng'], filtered_data['start_lat'])]
        gdf = gpd.GeoDataFrame(filtered_data, geometry=geometry)

        # 计算网格密度
        x_min, y_min, x_max, y_max = gdf.total_bounds
        x_grid = np.arange(x_min, x_max, grid_size)
        y_grid = np.arange(y_min, y_max, grid_size)

        # 创建网格并计算每个网格内的起点数量
        grid_geoms = []
        grid_data = []

        for i in range(len(x_grid) - 1):
            for j in range(len(y_grid) - 1):
                grid_geom = Point(x_grid[i] + grid_size/2, y_grid[j] + grid_size/2)
                count = len(gdf.cx[x_grid[i]:x_grid[i+1], y_grid[j]:y_grid[j+1]])

                grid_geoms.append(grid_geom)
                grid_data.append({
                    'density': count,
                    'grid_x': i,
                    'grid_y': j
                })

        # 创建GeoDataFrame
        density_gdf = gpd.GeoDataFrame(grid_data, geometry=grid_geoms)
        return density_gdf

    def generate_heatmap_with_grid(self, period_type, grid_shp_path, output_path=None, use_composite_score=False):
        """使用已有的网格shp文件生成热力图
        period_type: 时间段类型
        grid_shp_path: 网格shp文件路径 (必须提供)
        output_path: 热力图输出路径
        """
        # 1. 筛选数据
        filtered_data = self.filter_time_period(period_type)
        if filtered_data is None:
            return

        # 2. 检查并读取网格shp文件
        if not grid_shp_path or not os.path.exists(grid_shp_path):
            print(f"错误: 请提供有效的网格shp文件路径。当前路径: {grid_shp_path}")
            return

        try:
            # 读取网格shp文件
            grid_gdf = gpd.read_file(grid_shp_path)
            print(f"成功读取网格shp文件: {grid_shp_path}")
        except Exception as e:
            print(f"读取网格shp文件失败: {e}")
            return

        # 为网格数据添加唯一标识符
        if 'grid_id' not in grid_gdf.columns:
            grid_gdf = grid_gdf.reset_index().rename(columns={grid_gdf.index.name or 'index': 'grid_id'})
        print(f"网格数据列名: {grid_gdf.columns.tolist()}")

        if use_composite_score:
            print("使用综合得分绘制热力图...")
            # 准备点数据用于空间连接
            geometry = [Point(xy) for xy in zip(filtered_data['start_lng'], filtered_data['start_lat'])]
            points_gdf = gpd.GeoDataFrame(filtered_data, geometry=geometry)
            points_gdf.crs = 'EPSG:4326'
            
            # 确保网格数据和点数据具有相同的CRS
            if points_gdf.crs != grid_gdf.crs:
                points_gdf = points_gdf.to_crs(grid_gdf.crs)
                print(f"已将点数据转换为网格数据的CRS: {grid_gdf.crs}")
            
            # 空间连接：将点数据与网格匹配
            point_in_grid = gpd.sjoin(points_gdf, grid_gdf, how='left', predicate='within')
            print(f"空间连接完成，匹配到网格的点数: {len(point_in_grid[point_in_grid['grid_id'].notna()])}")
            
            # 计算区域活跃度指数
            activity_index_df = self.calculate_area_activity_index()
            if activity_index_df is None:
                print("计算区域活跃度指数失败，使用默认密度绘制热力图")
                use_composite_score = False
            else:
                # 将活跃度指数关联到网格
                # 1. 先将地理哈希位置转换为经纬度
                # 2. 创建点数据
                # 3. 空间连接到网格
                # 简化实现：直接使用点数据中的grid_id
                
                # 计算商业连接度指数
                connectivity_df = self.calculate_connectivity_score()
                if connectivity_df is None:
                    print("计算商业连接度指数失败，使用活跃度指数绘制热力图")
                    
                    # 按网格ID聚合活跃度指数
                    grid_activity = point_in_grid.merge(activity_index_df, left_on='geohashed_start_loc', right_on='location', how='left')
                    grid_activity = grid_activity.groupby('grid_id')['activity_index'].mean().reset_index()
                    
                    # 合并活跃度指数到网格数据
                    grid_gdf = grid_gdf.merge(grid_activity, on='grid_id', how='left')
                    grid_gdf['activity_index'] = grid_gdf['activity_index'].fillna(0)
                    grid_gdf['composite_score'] = grid_gdf['activity_index']
                else:
                    # 合并活跃度指数和连接度指数到网格
                    # 1. 按网格ID聚合活跃度指数
                    grid_activity = point_in_grid.merge(activity_index_df, left_on='geohashed_start_loc', right_on='location', how='left')
                    grid_activity = grid_activity.groupby('grid_id')['activity_index'].mean().reset_index()
                    
                    # 2. 按网格ID聚合连接度指数
                    grid_connectivity = point_in_grid.merge(connectivity_df, left_on='geohashed_start_loc', right_on='location', how='left')
                    grid_connectivity = grid_connectivity.groupby('grid_id')['connectivity_score'].mean().reset_index()
                    
                    # 3. 合并到网格数据
                    grid_gdf = grid_gdf.merge(grid_activity, on='grid_id', how='left')
                    grid_gdf = grid_gdf.merge(grid_connectivity, on='grid_id', how='left')
                    grid_gdf = grid_gdf.fillna(0)
                    
                    # 计算综合得分
                    grid_gdf['composite_score'] = (
                        grid_gdf['activity_index'] * 0.6 + 
                        grid_gdf['connectivity_score'] * 100 * 0.4  # 连接度归一化到0-100
                    )
        
        # 如果不使用综合得分，计算网格内的点数量
        if not use_composite_score:
            print(f"筛选后的数据量: {len(filtered_data)}")
            geometry = [Point(xy) for xy in zip(filtered_data['start_lng'], filtered_data['start_lat'])]
            points_gdf = gpd.GeoDataFrame(geometry=geometry)
            
            # 设置点数据的CRS为WGS84 (EPSG:4326)，然后转换为网格数据的CRS
            points_gdf.crs = 'EPSG:4326'
            print(f"点数据的原始CRS: {points_gdf.crs}")
            print(f"网格数据的CRS: {grid_gdf.crs}")
            
            # 转换点数据到网格数据的CRS
            points_gdf = points_gdf.to_crs(grid_gdf.crs)
            print(f"转换后的点数据地理范围: {points_gdf.total_bounds}")
            print(f"网格数据的地理范围: {grid_gdf.total_bounds}")

            # 空间连接计算每个网格内的点数
            grid_counts = gpd.sjoin(grid_gdf, points_gdf, how='left', predicate='contains')
            print(f"空间连接后的记录数: {len(grid_counts)}")
            print(f"空间连接后的数据列名: {grid_counts.columns.tolist()}")
            
            # 使用唯一标识符进行分组
            group_col = 'grid_id'
            grid_counts = grid_counts.groupby(group_col).size().reset_index(name='density')
            print(f"网格密度统计: {grid_counts['density'].describe()}")
            
            # 合并密度数据回原始网格
            grid_gdf = grid_gdf.merge(grid_counts, on=group_col, how='left')
            grid_gdf['density'] = grid_gdf['density'].fillna(0)

            # 确保density字段存在且类型正确
            grid_gdf['density'] = grid_gdf['density'].astype(float)

        # 3. 绘制热力图
        fig, ax = plt.subplots(1, 1, figsize=(12, 10))

        # 创建更具对比度的自定义颜色映射
        colors = ['#FFFFFF', '#FFFFCC', '#FFCC99', '#FF9966', '#FF6633', '#FF3300', '#CC0000', '#990000', '#660000']
        cmap = LinearSegmentedColormap.from_list('custom_cmap', colors, N=256)

        if use_composite_score:
            # 使用综合得分
            value_field = 'composite_score'
            # 对数变换综合得分，增强低中得分区域的对比度
            grid_gdf['log_value'] = np.log1p(grid_gdf[value_field])
            vmin = grid_gdf['log_value'].quantile(0.05)
            vmax = grid_gdf['log_value'].quantile(0.95)
            print(f"对数变换后的综合得分范围: {vmin} - {vmax}")
            # 绘制网格
            grid_gdf.plot(column='log_value', cmap=cmap, linewidth=0.5, ax=ax, edgecolor='0.5', alpha=0.8, legend=False, vmin=vmin, vmax=vmax)
            # 添加颜色条标签
            cbar = ax.get_figure().colorbar(ax.collections[0])
            cbar.set_label('对数综合得分 (log(1+score))', rotation=270, labelpad=20)
        else:
            # 对数变换密度值，增强低中密度区域的对比度
            grid_gdf['log_density'] = np.log1p(grid_gdf['density'])  # 使用log(1+x)变换避免log(0)问题

            # 使用分位数设置vmin和vmax，忽略极端值
            vmin = grid_gdf['log_density'].quantile(0.05)  # 5%分位数
            vmax = grid_gdf['log_density'].quantile(0.95)  # 95%分位数
            print(f"对数变换后的值范围: {vmin} - {vmax}")

            # 绘制网格
            grid_gdf.plot(column='log_density', cmap=cmap, linewidth=0.5, ax=ax, edgecolor='0.5', alpha=0.8, legend=False, vmin=vmin, vmax=vmax)

            # 添加颜色条标签
            cbar = ax.get_figure().colorbar(ax.collections[0])
            cbar.set_label('对数人流量 (log(1+count))', rotation=270, labelpad=20)

        # 如果提供了推荐位置，在热力图上标注
        # if recommendations is not None:
        #     # 确保推荐位置有经纬度信息
        #     if 'lng' in recommendations.columns and 'lat' in recommendations.columns:
        #         # 绘制推荐位置
        #         ax.scatter(recommendations['lng'], recommendations['lat'], color='red', s=100, marker='*', label='推荐位置')
                
        #         # 添加推荐位置标签
        #         for i, row in recommendations.iterrows():
        #             ax.annotate(f"TOP {i+1}", (row['lng'], row['lat']),
        #                         xytext=(5, 5), textcoords='offset points',
        #                         fontsize=10, fontweight='bold', color='black')
                
        #         # 添加图例
        #         ax.legend()
        #     else:
        #         print("警告: 推荐位置数据中没有'lng'和'lat'列，无法在热力图上标注")

    
        # 设置标题和标签
        period_names = {
            'weekday': '工作日',
            'weekend': '周末',
            'morning_peak': '早高峰 (7:00-9:00)',
            'evening_peak': '晚高峰 (17:00-19:00)'
        }
        title = f'{period_names.get(period_type, period_type)}人流量热力图'
        ax.set_title(title, fontsize=16)
        ax.set_xlabel('经度', fontsize=14)
        ax.set_ylabel('纬度', fontsize=14)

        # 保存或显示图形
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            print(f"热力图已保存至: {output_path}")
        else:
            plt.show()

        return grid_gdf

    def generate_business_metrics(self):
        """生成商业选址相关指标"""
        if self.processed_data is None:
            print("请先预处理数据")
            return None
            
        # 1. 热门起点分析
        popular_starts = self.processed_data['geohashed_start_loc'].value_counts().reset_index()
        popular_starts.columns = ['location', 'count']
        popular_starts['popularity_score'] = popular_starts['count'] / popular_starts['count'].max()
        
        # 2. 时段分析
        hourly_counts = self.processed_data.groupby('hour').size().reset_index(name='count')
        hourly_counts['hourly_index'] = hourly_counts['count'] / hourly_counts['count'].max()
        
        # 3. 车辆类型分布
        bike_type_dist = self.processed_data['biketype'].value_counts().reset_index()
        bike_type_dist.columns = ['biketype', 'count']
        
        return {
            'popular_starts': popular_starts,
            'hourly_counts': hourly_counts,
            'bike_type_dist': bike_type_dist
        }
        
    def calculate_area_activity_index(self):
        """计算区域活跃度指数
        区域分值 = log(起点流量) × 0.4 + 终点流量 × 0.3 + 夜间流量 × 0.3
        """
        if self.processed_data is None:
            print("请先预处理数据")
            return None
            
        # 计算起点流量
        start_counts = self.processed_data['geohashed_start_loc'].value_counts().reset_index()
        start_counts.columns = ['location', 'start_count']
        
        # 计算终点流量
        end_counts = self.processed_data['geohashed_end_loc'].value_counts().reset_index()
        end_counts.columns = ['location', 'end_count']
        
        # 计算夜间流量 (22:00-6:00)
        night_data = self.processed_data[(self.processed_data['hour'] >= 22) | (self.processed_data['hour'] < 6)]
        night_counts = night_data['geohashed_start_loc'].value_counts().reset_index()
        night_counts.columns = ['location', 'night_count']
        
        # 合并数据
        area_metrics = start_counts.merge(end_counts, on='location', how='outer')
        area_metrics = area_metrics.merge(night_counts, on='location', how='outer')
        
        # 填充缺失值
        area_metrics = area_metrics.fillna(0)
        
        # 计算区域活跃度指数
        area_metrics['log_start_count'] = np.log1p(area_metrics['start_count'])  # 使用log(1+x)避免log(0)
        area_metrics['start_score'] = area_metrics['log_start_count'] / area_metrics['log_start_count'].max()
        area_metrics['end_score'] = area_metrics['end_count'] / area_metrics['end_count'].max() if area_metrics['end_count'].max() > 0 else 0
        area_metrics['night_score'] = area_metrics['night_count'] / area_metrics['night_count'].max() if area_metrics['night_count'].max() > 0 else 0
        
        area_metrics['activity_index'] = (
            area_metrics['start_score'] * 0.4 + 
            area_metrics['end_score'] * 0.3 + 
            area_metrics['night_score'] * 0.3
        )
        
        # 归一化指数到0-100
        area_metrics['activity_index'] = (area_metrics['activity_index'] / area_metrics['activity_index'].max()) * 100
        print(area_metrics)

        print("区域活跃度指数计算完成")
        return area_metrics.sort_values('activity_index', ascending=False)
        
    def calculate_connectivity_score(self, top_n=100):
        """计算商业连接度指数
        连接度 = (流入量 + 流出量) / (最大流入量 + 最大流出量)
        
        参数:
        top_n: int, 只考虑前N个热门起点和终点，减少计算量
        """
        if self.processed_data is None:
            print("请先预处理数据")
            return None
            
        # 找到热门起点和终点
        print("寻找热门起点和终点...")
        popular_starts = self.processed_data['geohashed_start_loc'].value_counts().nlargest(top_n).index
        popular_ends = self.processed_data['geohashed_end_loc'].value_counts().nlargest(top_n).index
        
        # 筛选数据，只保留热门起点和终点
        filtered_data = self.processed_data[
            self.processed_data['geohashed_start_loc'].isin(popular_starts) & 
            self.processed_data['geohashed_end_loc'].isin(popular_ends)
        ]
        
        # 检查筛选后的数据是否为空
        if filtered_data.empty:
            print(f"警告: 筛选后的数据为空，尝试降低top_n值或检查数据质量")
            # 使用所有数据进行计算
            filtered_data = self.processed_data
            print("已使用所有数据进行连接度计算")
        
        # 确保filtered_data不为空
        if filtered_data.empty:
            print("错误: 没有可用数据计算连接度")
            return None
        
        # 构建OD矩阵
        print(f"构建精简OD矩阵(仅包含前{top_n}个热门起点和终点)...")
        od_matrix = filtered_data.groupby(['geohashed_start_loc', 'geohashed_end_loc']).size().unstack(fill_value=0)
        
        # 计算流入量和流出量
        inbound = od_matrix.sum(axis=0)  # 流入量
        outbound = od_matrix.sum(axis=1)  # 流出量
          
        # 检查是否有流量数据
        if inbound.empty and outbound.empty:
            print("警告: 没有流量数据，无法计算连接度")
            return None
        
        max_inbound = inbound.max() if not inbound.empty else 0
        max_outbound = outbound.max() if not outbound.empty else 0
        max_traffic = max(max_inbound, max_outbound)
        
        # 避免分母为0
        if max_inbound + max_outbound == 0:
            print("警告: 总流量为0，无法计算连接度")
            return None
        
        if max_traffic == 0:
            print("警告: 没有流量数据，无法计算连接度")
            return None
            
        # 计算每个区域的连接度
        connectivity = (inbound + outbound) / (max_inbound + max_outbound)
        connectivity_df = connectivity.reset_index()
        connectivity_df.columns = ['location', 'connectivity_score']
        print(connectivity_df)

        print("商业连接度指数计算完成")
        return connectivity_df.sort_values('connectivity_score', ascending=False)
        
    def analyze_business_type_fit(self, activity_index_df):
        """分析业态适配系数
        根据区域特征推荐适合的商业类型
        """
        if activity_index_df is None:
            print("请先计算区域活跃度指数")
            return None
            
        if self.processed_data is None:
            print("请先预处理数据")
            return None
            
        # 计算夜间流量占比
        night_data = self.processed_data[(self.processed_data['hour'] >= 20) | (self.processed_data['hour'] < 6)]
        # print(night_data)
        
        # 检查夜间数据是否为空
        if night_data.empty:
            print("警告: 没有夜间流量数据")
            # 创建一个空的night_counts DataFrame
            night_counts = pd.DataFrame({
                'location': activity_index_df['location'].unique(),
                'night_count': 0
            })
        else:
            night_counts = night_data['geohashed_start_loc'].value_counts().reset_index()
            night_counts.columns = ['location', 'night_count']
        # print(night_counts)    

        
        # 确保activity_index_df有'location'列
        if 'location' not in activity_index_df.columns:
            print("错误: activity_index_df中没有'location'列")
            return None

        
        # 合并夜间流量数
        # fit_df['night_count']=night_counts['night_count'].fillna(0)
        fit_df = activity_index_df.merge(night_counts, on='location', how='left')
        fit_df['night_count']=night_counts['night_count'].fillna(0)
        fit_df['start_count']=self.processed_data['geohashed_start_loc'].value_counts().reset_index()['count']


        # 检查是否有'start_count'列
        if 'start_count' not in fit_df.columns:
            print("错误: 合并后的DataFrame中没有'start_count'列")
            return None
        
        # 计算夜间流量占比
        fit_df['night_ratio'] = fit_df['night_count'] / fit_df['start_count']
        fit_df['night_ratio'] = fit_df['night_ratio'].fillna(0)
        print(fit_df['night_count'])
        print(fit_df['start_count'])

        print(fit_df['night_ratio'])

        # 计算短途骑行占比 (假设小于2公里为短途)
        # 实际应用中应根据经纬度计算距离，这里简化处理
        # 创建起点和终点的点几何
        start_geometry = [Point(xy) for xy in zip(self.processed_data['start_lng'], self.processed_data['start_lat'])]
        end_geometry = [Point(xy) for xy in zip(self.processed_data['end_lng'], self.processed_data['end_lat'])]
        
        # 计算距离 (单位: 度，需要转换为公里)
        self.processed_data['distance'] = [start.distance(end) for start, end in zip(start_geometry, end_geometry)]
        #  approximate conversion: 1度 ≈ 111公里
        self.processed_data['distance_km'] = self.processed_data['distance'] * 111
        
        # 计算短途骑行占比
        short_trips = self.processed_data[self.processed_data['distance_km'] < 2]
        short_trip_counts = short_trips['geohashed_start_loc'].value_counts().reset_index()
        short_trip_counts.columns = ['location', 'short_trip_count']
        
        fit_df = fit_df.merge(short_trip_counts, on='location', how='left')
        print(fit_df['short_trip_count'])
        

        fit_df['short_trip_count'] = fit_df['short_trip_count'].fillna(0)
        fit_df['short_trip_ratio'] = fit_df['short_trip_count'] / fit_df['start_count']
        print(fit_df['short_trip_ratio'])
        # 计算工作日高峰占比
        weekday_peak_data = self.processed_data[(self.processed_data['is_weekend'] == 0) & 
                                                ((self.processed_data['hour'].between(7, 9)) | 
                                                 (self.processed_data['hour'].between(17, 19)))]
        weekday_peak_counts = weekday_peak_data['geohashed_start_loc'].value_counts().reset_index()
        weekday_peak_counts.columns = ['location', 'weekday_peak_count']
        
        fit_df = fit_df.merge(weekday_peak_counts, on='location', how='left')
        fit_df['weekday_peak_count'] = fit_df['weekday_peak_count'].fillna(0)
        print(fit_df['weekday_peak_count'])
        fit_df['weekday_peak_ratio'] = fit_df['weekday_peak_count'] / fit_df['start_count']
        print(fit_df['weekday_peak_ratio'])
        # 移除推荐业态相关代码
        # # 推荐业态
        # def recommend_business_types(row):
        #     recommendations = []
        #     
        #     # 餐饮/娱乐
        #     if row['night_ratio'] > 0.2:
        #         recommendations.append(('餐饮/娱乐', 3))
        #     
        #     # 便利店/咖啡厅
        #     if row['short_trip_ratio'] > 0.5:
        #         recommendations.append(('便利店/咖啡厅', 2.5))
        #     
        #     # 商务餐饮/快递站点
        #     if row['weekday_peak_ratio'] > 0.4:
        #         recommendations.append(('商务餐饮/快递站点', 3))
        #     
        #     return recommendations
        # 
        # fit_df['recommended_business'] = fit_df.apply(recommend_business_types, axis=1)
        
        print("区域分析完成")
        return fit_df
        
    # # 移除商业推荐相关方法
    def generate_business_recommendations(self, grid_shp_path):
        """生成商业选址推荐
        grid_shp_path: 网格shp文件路径
        top_n: 返回前N个推荐位置
        """
        # 1. 计算区域活跃度指数
        activity_index_df = self.calculate_area_activity_index()
        if activity_index_df is None:
            return None
            
        # 2. 计算商业连接度指数
        connectivity_df = self.calculate_connectivity_score()
        if connectivity_df is None:
            return None
            
        # 3. 合并指标
        recommendations = activity_index_df.merge(connectivity_df, on='location', how='outer')
        recommendations = recommendations.fillna(0)
        
        # 4. 计算综合得分
        recommendations['composite_score'] = (
            recommendations['activity_index'] * 0.6 + 
            recommendations['connectivity_score'] * 100 * 0.4  # 连接度归一化到0-100
        )
 
    #     
    #     # 5. 分析业态适配
    #     recommendations = self.analyze_business_type_fit(recommendations)
    #     
        # 5. 解码地理坐标
        def decode_geohash(geohash):
            try:
                lat, lng = geohash2.decode(geohash)
                return lng, lat
            except:
                return 0, 0
            
        recommendations[['lng', 'lat']] = recommendations['location'].apply(
            lambda x: pd.Series(decode_geohash(x))
        )
    #     
    #     # 7. 可视化推荐结果
    #     output_dir = 'results/recommendations'
    #     os.makedirs(output_dir, exist_ok=True)
        
        # 绘制前N个推荐位置
        # 移除商业推荐部分
        # top_recommendations = recommendations.nlargest(top_n, 'composite_score')
        
        # # 保存推荐结果
        # top_recommendations.to_csv(f'{output_dir}/top_{top_n}_recommendations.csv', index=False)
        # print(f"商业选址推荐已保存至 {output_dir}/top_{top_n}_recommendations.csv")
        
        # # 可视化推荐位置
        # plt.figure(figsize=(12, 10))
        # plt.scatter(top_recommendations['lng'], top_recommendations['lat'], c=top_recommendations['composite_score'], cmap='viridis', s=100)
        # plt.colorbar(label='综合得分')
        # plt.title(f'前{top_n}个商业选址推荐位置')
        # plt.xlabel('经度')
        # plt.ylabel('纬度')
        # plt.grid(True)
        # plt.savefig(f'{output_dir}/top_{top_n}_recommendations.png', dpi=300)
        # print(f"推荐位置可视化图已保存至 {output_dir}/top_{top_n}_recommendations.png")
        
        return None

if __name__ == '__main__':
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='处理摩拜单车数据并生成热力图及商业选址推荐')
    parser.add_argument('--grid-shp', required=True, help='网格shp文件路径')
    parser.add_argument('--top-n', type=int, default=10, help='返回前N个推荐位置')
    args = parser.parse_args()
    
    # 创建处理器实例
    processor = TrainDataProcessor()
    
    # 加载数据
    print("正在加载数据...")
    if not processor.load_data():
        print("数据加载失败，程序退出")
        exit(1)
    
    # 预处理数据
    print("正在预处理数据...")
    if not processor.preprocess_data():
        print("数据预处理失败，程序退出")
        exit(1)
    
    # 生成商业指标 (为调试暂时禁用)
    # print("正在生成商业指标...")
    # business_metrics = processor.generate_business_metrics()
    # if business_metrics:
    #     print(f"热门起点数量: {len(business_metrics['popular_starts'])}")
    #     print(f"时段分布数量: {len(business_metrics['hourly_counts'])}")
    #     print(f"车辆类型数量: {len(business_metrics['bike_type_dist'])}种")
    # 
    # # 暂时禁用商业指标输出
    # # print(f"时段分布数量: {len(business_metrics['hourly_counts'])}")
    # # print(f"车辆类型数量: {len(business_metrics['bike_type_dist'])}种")
    
    # 生成不同时间段的热力图
    print("正在生成热力图...")
    output_dir = 'results/heatmaps'
    os.makedirs(output_dir, exist_ok=True)
    
    # 绘制工作日热力图
    processor.generate_heatmap_with_grid('weekday', args.grid_shp, f'{output_dir}/weekday_heatmap.png', use_composite_score=True)
    
    # 绘制周末热力图
    processor.generate_heatmap_with_grid('weekend', args.grid_shp, f'{output_dir}/weekend_heatmap.png', use_composite_score=True)
    
    # 绘制早高峰热力图
    processor.generate_heatmap_with_grid('morning_peak', args.grid_shp, f'{output_dir}/morning_peak_heatmap.png', use_composite_score=True)
    
    # 绘制晚高峰热力图
    processor.generate_heatmap_with_grid('evening_peak', args.grid_shp, f'{output_dir}/evening_peak_heatmap.png', use_composite_score=True)
    
    print("所有热力图绘制完成，保存在results/heatmaps目录下")
    # 移除商业推荐相关输出
    # print("商业选址推荐系统已完成！")
    # print(f"推荐结果已保存至 results/recommendations/top_{args.top_n}_recommendations.csv")